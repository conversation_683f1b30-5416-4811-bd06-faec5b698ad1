# 🔌 HƯỚNG DẪN KẾT NỐI ĐỘNG CƠ DC

## ⚡ Phần cứng cần thiết:

### 1. **Driver động cơ L298N** (<PERSON><PERSON><PERSON>ến nghị)
- Mo<PERSON>le điều khiển động cơ DC phổ biến
- Hỗ trợ điều khiển hướng và tốc độ
- <PERSON><PERSON> bảo vệ quá tải

### 2. **Động cơ DC 1 chiều**
- Điện áp: 3V-12V
- Dòng điện: < 2A (tùy thuộc driver)

### 3. **Nguồn điện riêng cho động cơ**
- 5V-12V (tùy động cơ)
- Dòng điện đủ lớn cho động cơ

## 🔗 Sơ đồ kết nối với L298N:

```
ESP8266 NodeMCU    →    L298N <PERSON><PERSON><PERSON>
─────────────────────────────────────
D2 (GPIO4)         →    IN1
D1 (GPIO5)         →    IN2  
D5 (GPIO14)        →    ENA (Enable A)
GND                →    GND
5V                 →    VCC (Logic)

L298N              →    <PERSON>ộng cơ DC
─────────────────────────────────────
OUT1               →    Dây động cơ (+)
OUT2               →    Dây động cơ (-)

Nguồn ngoài        →    L298N
─────────────────────────────────────
(+) 6V-12V         →    VIN (Motor Power)
(-) GND            →    GND
```

## 📋 Chi tiết kết nối:

### ESP8266 → L298N:
- **D2 (GPIO4)** → **IN1**: Điều khiển hướng 1
- **D1 (GPIO5)** → **IN2**: Điều khiển hướng 2  
- **D5 (GPIO14)** → **ENA**: PWM điều khiển tốc độ
- **GND** → **GND**: Đất chung
- **3V3** → **VCC**: Nguồn logic (nếu có)

### Nguồn điện:
- **Nguồn ESP8266**: 5V từ USB hoặc adapter
- **Nguồn động cơ**: 6V-12V riêng biệt (quan trọng!)

## ⚠️ LƯU Ý QUAN TRỌNG:

### 1. **Nguồn điện riêng biệt:**
- KHÔNG dùng chung nguồn ESP8266 cho động cơ
- Động cơ cần nguồn riêng 6V-12V
- Nối GND chung giữa ESP8266 và nguồn động cơ

### 2. **Bảo vệ mạch:**
- Sử dụng driver L298N để bảo vệ ESP8266
- KHÔNG kết nối trực tiếp động cơ vào ESP8266
- Kiểm tra cực tính trước khi cấp nguồn

### 3. **Kiểm tra kết nối:**
- Đảm bảo tất cả dây nối chắc chắn
- Kiểm tra không có chập mạch
- Test từng bước: LED → Driver → Động cơ

## 🔧 Cấu hình trong code:

### Thay đổi chân kết nối (nếu cần):
```cpp
const int MOTOR_PIN1 = 4;  // D2 - Hướng 1
const int MOTOR_PIN2 = 5;  // D1 - Hướng 2  
const int MOTOR_ENABLE = 14; // D5 - PWM tốc độ
```

### Điều chỉnh tốc độ mặc định:
```cpp
int motorSpeed = 255; // 0-255 (0=dừng, 255=tối đa)
```

## 🎮 Cách sử dụng:

### Trên giao diện web:
1. **THUẬN**: Động cơ quay theo chiều kim đồng hồ
2. **NGHỊCH**: Động cơ quay ngược chiều kim đồng hồ
3. **DỪNG**: Dừng động cơ
4. **Thanh trượt**: Điều chỉnh tốc độ (0-255)

### Logic điều khiển:
- **Thuận**: IN1=HIGH, IN2=LOW, ENA=PWM
- **Nghịch**: IN1=LOW, IN2=HIGH, ENA=PWM  
- **Dừng**: IN1=LOW, IN2=LOW, ENA=0

## 🛠️ Troubleshooting:

### Động cơ không quay:
- [ ] Kiểm tra nguồn điện động cơ
- [ ] Kiểm tra kết nối dây
- [ ] Thử tăng tốc độ (>100)
- [ ] Kiểm tra driver L298N

### Động cơ quay sai hướng:
- [ ] Đổi chỗ OUT1 và OUT2 trên L298N
- [ ] Hoặc đổi chỗ MOTOR_PIN1 và MOTOR_PIN2 trong code

### ESP8266 reset liên tục:
- [ ] Kiểm tra nguồn điện riêng biệt
- [ ] Đảm bảo không dùng chung nguồn
- [ ] Kiểm tra dòng điện động cơ

### Tốc độ không thay đổi:
- [ ] Kiểm tra kết nối chân ENA
- [ ] Đảm bảo jumper ENA được tháo ra
- [ ] Kiểm tra PWM hoạt động

## 🔍 Test từng bước:

### Bước 1: Test LED
- Upload code và test LED trước
- Đảm bảo ESP8266 hoạt động bình thường

### Bước 2: Test driver không tải
- Kết nối ESP8266 với L298N (chưa có động cơ)
- Test các tín hiệu điều khiển

### Bước 3: Test với động cơ
- Kết nối động cơ và nguồn riêng
- Test từng chức năng: thuận, nghịch, dừng

## 💡 Mẹo hay:

1. **Dùng LED để debug**: Kết nối LED vào các chân IN1, IN2 để xem tín hiệu
2. **Bắt đầu tốc độ thấp**: Test với tốc độ 100-150 trước
3. **Đo điện áp**: Dùng đồng hồ vạn năng kiểm tra nguồn
4. **Serial Monitor**: Theo dõi log để debug
