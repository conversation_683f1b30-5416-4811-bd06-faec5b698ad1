# 🚀 HƯỚNG DẪN NHANH - ESP8266 OFFLINE MODE

## ✅ Những gì đã thay đổi:

### 🔄 **Chế độ Hybrid (WiFi + Access Point)**
- ESP8266 sẽ TỰ ĐỘNG chuyển đổi giữa 2 chế độ
- **C<PERSON> WiFi**: Kết nối bình thường như trước
- **Không có WiFi**: Tạo mạng WiFi riêng để điện thoại kết nối

### 📱 **Cách sử dụng khi KHÔNG CÓ MẠNG:**

1. **Bật ESP8266** (không cần WiFi)
2. **Trên điện thoại**, tìm WiFi tên: `ESP8266_SmartHome`
3. **Kết nối** với mật khẩu: `12345678`
4. **Mở trình duyệt** và vào: `http://***********`
5. **Đi<PERSON><PERSON> khiển LED** nh<PERSON> bình thường!

### 🔧 **Tính năng thông minh:**
- ✅ Tự động phát hiện mất mạng → Chuyển sang AP mode
- ✅ Tự động phát hiện có mạng trở lại → Kết nối WiFi
- ✅ Kiểm tra kết nối mỗi 30 giây
- ✅ Hiển thị trạng thái trên giao diện web

## 🎯 **Kịch bản sử dụng:**

### Scenario 1: Có WiFi bình thường
```
ESP8266 → Kết nối WiFi → Truy cập qua IP router cấp
```

### Scenario 2: Mất WiFi đột ngột
```
ESP8266 → Phát hiện mất mạng → Tự động tạo AP → Điện thoại kết nối trực tiếp
```

### Scenario 3: WiFi trở lại
```
ESP8266 → Phát hiện WiFi → Tự động kết nối lại → Chuyển về chế độ WiFi
```

## 🛠️ **Tùy chỉnh (nếu cần):**

### Đổi tên WiFi của ESP8266:
```cpp
const char* ap_ssid = "TenBanMuon";
const char* ap_password = "MatKhauBanMuon";
```

### Đổi thời gian kiểm tra kết nối:
```cpp
// Thay 30000 = 30 giây thành giá trị khác
if (millis() - lastCheck > 30000) {
```

## 📋 **Checklist trước khi test:**

- [ ] Upload code mới lên ESP8266
- [ ] Mở Serial Monitor để xem log
- [ ] Test chế độ có WiFi
- [ ] Tắt WiFi router để test chế độ AP
- [ ] Kiểm tra tự động chuyển đổi

## 🆘 **Nếu có vấn đề:**

1. **Không thấy WiFi "ESP8266_SmartHome":**
   - Đợi 10-15 giây sau khi bật ESP8266
   - Kiểm tra Serial Monitor xem có thông báo AP mode không

2. **Không kết nối được:**
   - Đảm bảo mật khẩu đúng: `12345678`
   - Thử quên và kết nối lại WiFi

3. **Không tự động chuyển đổi:**
   - Kiểm tra nguồn điện ổn định
   - Xem log trong Serial Monitor

## 🎉 **Kết quả:**
Bây giờ ESP8266 của bạn có thể hoạt động **100% offline** và vẫn điều khiển được qua điện thoại!
