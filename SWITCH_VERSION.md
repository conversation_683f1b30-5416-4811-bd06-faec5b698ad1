# 🔄 Chuyển đổi giữa các phiên bản

## 📁 C<PERSON>u trúc file trong dự án

```
src/
├── main.cpp          # Phiên bản đầy đủ (LED + Motor + PWA)
└── simple_mdns.cpp   # Phiên bản đơn giản (chỉ mDNS + Web)
```

## 🚀 Cách chuyển đổi phiên bản

### Sử dụng phiên bản đơn giản (mDNS)

1. **Đổi tên file**:
   ```bash
   # Backup phiên bản hiện tại
   mv src/main.cpp src/main_full.cpp
   
   # Sử dụng phiên bản đơn giản
   mv src/simple_mdns.cpp src/main.cpp
   ```

2. **Upload chương trình**:
   ```bash
   pio run --target upload
   pio device monitor
   ```

3. **Truy cập**: `http://esp.local`

### Quay lại phiên bản đầy đủ

1. **Đổi tên file**:
   ```bash
   # Backup phiên bản đơn giản
   mv src/main.cpp src/simple_mdns.cpp
   
   # Khôi phục phiên bản đầy đủ
   mv src/main_full.cpp src/main.cpp
   ```

2. **Upload chương trình**:
   ```bash
   pio run --target upload
   pio device monitor
   ```

3. **Truy cập**: `http://192.168.1.xxx` (IP hiển thị trên Serial)

## ⚙️ Cấu hình WiFi cho cả hai phiên bản

Cả hai file đều cần cấu hình WiFi:

### File `main.cpp` (phiên bản đầy đủ):
```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";
const char* password = "MAT_KHAU_WIFI";
```

### File `simple_mdns.cpp` (phiên bản đơn giản):
```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";
const char* password = "MAT_KHAU_WIFI";
const char* mdns_name = "esp";  // Tên miền cho mDNS
```

## 🔧 Sử dụng PlatformIO để chuyển đổi

### Cách 1: Sử dụng multiple environments

Thêm vào `platformio.ini`:

```ini
[env:esp12e]
platform = espressif8266
board = esp12e
framework = arduino
monitor_speed = 115200

[env:esp12e_simple]
platform = espressif8266
board = esp12e
framework = arduino
monitor_speed = 115200
src_filter = +<*> -<main.cpp> +<simple_mdns.cpp>
```

Sau đó build với:
```bash
# Phiên bản đầy đủ
pio run -e esp12e --target upload

# Phiên bản đơn giản
pio run -e esp12e_simple --target upload
```

### Cách 2: Sử dụng build flags

Thêm vào đầu file `main.cpp`:

```cpp
#ifdef SIMPLE_VERSION
  // Code cho phiên bản đơn giản
  #include "simple_mdns_code.h"
#else
  // Code cho phiên bản đầy đủ (code hiện tại)
#endif
```

Build với:
```bash
# Phiên bản đầy đủ
pio run --target upload

# Phiên bản đơn giản
pio run --target upload -D SIMPLE_VERSION
```

## 📋 So sánh tính năng

| Tính năng | Phiên bản đầy đủ | Phiên bản đơn giản |
|-----------|------------------|-------------------|
| **Kết nối WiFi** | ✅ | ✅ |
| **Web Server** | ✅ | ✅ |
| **mDNS (esp.local)** | ❌ | ✅ |
| **Điều khiển LED** | ✅ | ❌ |
| **Điều khiển Motor** | ✅ | ❌ |
| **Giao diện responsive** | ✅ | ❌ |
| **PWA Support** | ✅ | ❌ |
| **AP Mode fallback** | ✅ | ❌ |
| **Static IP** | ✅ | ❌ |
| **Hamburger menu** | ✅ | ❌ |
| **Dark theme** | ✅ | ❌ |

## 🎯 Khi nào sử dụng phiên bản nào?

### Sử dụng phiên bản đơn giản khi:
- ✅ Chỉ cần web server cơ bản
- ✅ Muốn truy cập qua tên miền (esp.local)
- ✅ Không cần điều khiển thiết bị
- ✅ Muốn code đơn giản, dễ hiểu
- ✅ Học tập về mDNS

### Sử dụng phiên bản đầy đủ khi:
- ✅ Cần điều khiển LED và motor
- ✅ Muốn giao diện đẹp, responsive
- ✅ Cần PWA cho mobile
- ✅ Muốn AP mode backup
- ✅ Cần static IP
- ✅ Dự án thực tế

## 🔍 Debug và troubleshooting

### Kiểm tra phiên bản đang chạy:
1. Mở Serial Monitor
2. Xem thông báo khởi động:
   - Phiên bản đơn giản: "ESP8266 mDNS Web Server Starting..."
   - Phiên bản đầy đủ: "Khởi động ESP8266..."

### Kiểm tra mDNS:
```bash
# Windows (cần Bonjour)
ping esp.local

# macOS/Linux
ping esp.local
avahi-browse -rt _http._tcp
```

### Kiểm tra web server:
- Phiên bản đơn giản: Trang thông tin hệ thống
- Phiên bản đầy đủ: Giao diện điều khiển với nút bấm

## 📝 Ghi chú

- Cả hai phiên bản đều sử dụng cùng cấu hình hardware (ESP8266)
- Chỉ cần thay đổi file source code, không cần thay đổi wiring
- Có thể kết hợp tính năng mDNS vào phiên bản đầy đủ nếu cần
