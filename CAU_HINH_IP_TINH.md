# 🌐 CẤU HÌNH IP TĨNH CHO ESP8266

## ❓ Vấn đề:
Mỗi lần cắm điện lại, ESP8266 nhận IP khác nhau từ router (DHCP), gây kh<PERSON> khăn trong việc truy cập.

## ✅ Giải pháp: IP Tĩnh (Static IP)

### 🔧 **Cấu hình trong code:**
```cpp
// Cấu hình IP tĩnh - Thay đổi theo mạng của bạn
IPAddress local_IP(192, 168, 1, 100);      // IP cố định cho ESP8266
IPAddress gateway(192, 168, 1, 1);         // Gateway (IP router)
IPAddress subnet(255, 255, 255, 0);        // Subnet mask
IPAddress primaryDNS(8, 8, 8, 8);          // DNS chính
IPAddress secondaryDNS(8, 8, 4, 4);        // DNS phụ
```

## 🔍 **Cách tìm thông tin mạng của bạn:**

### 📱 **Trên Windows:**
1. Mở **Command Prompt** (cmd)
2. Gõ: `ipconfig`
3. Tìm thông tin:
   - **Default Gateway**: Địa chỉ router (VD: ***********)
   - **Subnet Mask**: Thường là *************
   - **IPv4 Address**: Dải IP hiện tại (VD: 192.168.1.x)

### 📱 **Trên điện thoại:**
1. Vào **Settings** → **WiFi**
2. Nhấn vào tên WiFi đang kết nối
3. Xem thông tin:
   - **Gateway**: Địa chỉ router
   - **IP Address**: Dải IP hiện tại

### 🖥️ **Trên router:**
1. Truy cập router qua trình duyệt: `http://***********`
2. Đăng nhập (thường admin/admin)
3. Tìm **DHCP Settings** hoặc **LAN Settings**

## ⚙️ **Cách chọn IP phù hợp:**

### 📋 **Quy tắc chọn IP:**
1. **Cùng dải với router**: Nếu router là *********** → Chọn 192.168.1.x
2. **Tránh xung đột**: Chọn IP ngoài dải DHCP
3. **Dải DHCP thường**: *************-200
4. **IP an toàn**: ************-99 hoặc *************-254

### 💡 **Ví dụ cấu hình phổ biến:**

#### **Mạng 192.168.1.x:**
```cpp
IPAddress local_IP(192, 168, 1, 100);    // ESP8266 IP
IPAddress gateway(192, 168, 1, 1);       // Router IP
IPAddress subnet(255, 255, 255, 0);      // Subnet mask
```

#### **Mạng 192.168.0.x:**
```cpp
IPAddress local_IP(192, 168, 0, 100);    // ESP8266 IP
IPAddress gateway(192, 168, 0, 1);       // Router IP
IPAddress subnet(255, 255, 255, 0);      // Subnet mask
```

#### **Mạng 10.0.0.x:**
```cpp
IPAddress local_IP(10, 0, 0, 100);       // ESP8266 IP
IPAddress gateway(10, 0, 0, 1);          // Router IP
IPAddress subnet(255, 255, 255, 0);      // Subnet mask
```

## 🔧 **Cách cấu hình:**

### 1. **Tìm thông tin mạng hiện tại:**
```bash
# Windows
ipconfig

# Linux/Mac
ifconfig
```

### 2. **Cập nhật code:**
```cpp
// Thay đổi các giá trị này theo mạng của bạn
IPAddress local_IP(192, 168, 1, 100);      // IP bạn muốn
IPAddress gateway(192, 168, 1, 1);         // IP router của bạn
IPAddress subnet(255, 255, 255, 0);        // Subnet của bạn
```

### 3. **Upload và test:**
1. Upload code mới lên ESP8266
2. Mở Serial Monitor
3. Kiểm tra log cấu hình IP
4. Truy cập IP cố định: `http://*************`

## 📊 **Thông tin hiển thị trong sidebar:**

### 🔍 **Thông tin mạng mới:**
- **IP Address**: IP hiện tại của ESP8266
- **Gateway**: Địa chỉ router
- **Subnet**: Subnet mask
- **DNS**: DNS server đang sử dụng
- **Chế độ**: WiFi hoặc AP mode

## ⚠️ **Lưu ý quan trọng:**

### 🚨 **Tránh xung đột IP:**
1. **Kiểm tra IP đã sử dụng**: Ping IP trước khi dùng
2. **Tránh dải DHCP**: Không chọn IP trong dải DHCP
3. **Ghi chú IP**: Lưu lại IP đã dùng để tránh trùng

### 🔧 **Troubleshooting:**

#### **Không kết nối được WiFi:**
- Kiểm tra Gateway đúng chưa
- Thử IP khác trong cùng dải
- Kiểm tra Subnet mask

#### **Kết nối WiFi nhưng không truy cập được:**
- Ping IP từ máy tính: `ping *************`
- Kiểm tra firewall
- Thử truy cập từ điện thoại

#### **IP bị xung đột:**
- Thay đổi IP cuối (VD: 100 → 101)
- Kiểm tra thiết bị khác trong mạng

## 🎯 **Lợi ích IP tĩnh:**

### ✅ **Ưu điểm:**
- **IP cố định**: Luôn cùng địa chỉ
- **Dễ nhớ**: Không cần tìm IP mới
- **Ổn định**: Không bị thay đổi
- **Bookmark**: Có thể lưu bookmark

### ⚡ **Kết quả:**
```
Trước: http://************* (thay đổi mỗi lần)
Sau:   http://************* (cố định mãi mãi)
```

## 📝 **Template cấu hình:**

### 📋 **Điền thông tin mạng của bạn:**
```cpp
// Thay đổi theo mạng của bạn:
IPAddress local_IP(192, 168, 1, 100);      // [Dải mạng].100
IPAddress gateway(192, 168, 1, 1);         // [IP Router]
IPAddress subnet(255, 255, 255, 0);        // [Subnet Mask]
IPAddress primaryDNS(8, 8, 8, 8);          // Google DNS
IPAddress secondaryDNS(8, 8, 4, 4);        // Google DNS
```

Bây giờ ESP8266 sẽ luôn có cùng một địa chỉ IP! 🎉
