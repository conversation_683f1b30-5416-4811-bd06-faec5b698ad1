# ESP8266 Smart Home - Chế độ Offline

## Tổng quan
Ứng dụng ESP8266 Smart Home đã được cập nhật để có thể hoạt động ngay cả khi không có kết nối WiFi internet. Hệ thống sẽ tự động chuyển đổi giữa các chế độ hoạt động.

## Các chế độ hoạt động

### 1. Chế độ WiFi (Ưu tiên)
- ESP8266 kết nối vào mạng WiFi hiện có
- C<PERSON> thể truy cập từ bất kỳ thiết bị nào trong cùng mạng
- IP động được cấp bởi router

### 2. Chế độ Access Point (AP) - Backup
- ESP8266 tạo ra mạng WiFi riêng
- Điện thoại kết nối trực tiếp vào ESP8266
- <PERSON>h<PERSON>ng cần internet, hoạt động hoàn toàn offline

## Cách hoạt động

### Khởi động lần đầu:
1. ESP8266 thử kết nối WiFi trong 10 giây
2. **Nếu thành công**: Chạy ở chế độ WiFi
3. **Nếu thất bại**: Tự động chuyển sang chế độ AP

### Trong quá trình hoạt động:
- Kiểm tra kết nối WiFi mỗi 30 giây
- Tự động chuyển đổi giữa các chế độ khi cần thiết
- Thông báo trạng thái qua Serial Monitor

## Thông tin kết nối

### Chế độ WiFi:
- **SSID**: Dau Lac
- **IP**: Được cấp tự động bởi router
- **Truy cập**: http://[IP_ADDRESS]

### Chế độ Access Point:
- **SSID**: ESP8266_SmartHome
- **Password**: 12345678
- **IP**: ***********
- **Truy cập**: http://***********

## Hướng dẫn sử dụng

### Khi có WiFi:
1. Mở Serial Monitor để xem IP address
2. Truy cập IP đó từ trình duyệt điện thoại

### Khi không có WiFi:
1. Tìm mạng WiFi "ESP8266_SmartHome" trên điện thoại
2. Kết nối với mật khẩu: 12345678
3. Mở trình duyệt và truy cập: http://***********

## Tính năng mới

### Tự động chuyển đổi:
- Phát hiện mất kết nối WiFi → Chuyển sang AP
- Phát hiện WiFi khả dụng → Thử kết nối lại

### Hiển thị trạng thái:
- Giao diện web hiển thị chế độ hiện tại
- IP address tương ứng với chế độ
- Thông tin uptime và trạng thái kết nối

## Tùy chỉnh

### Thay đổi thông tin Access Point:
```cpp
const char* ap_ssid = "TenWiFiCuaBan";
const char* ap_password = "MatKhauCuaBan";
```

### Thay đổi thời gian kiểm tra kết nối:
```cpp
// Trong hàm loop(), thay đổi 30000 (30 giây)
if (millis() - lastCheck > 30000) {
```

## Lợi ích

1. **Độ tin cậy cao**: Hoạt động ngay cả khi mất mạng
2. **Tự động phục hồi**: Tự động kết nối lại khi có mạng
3. **Dễ sử dụng**: Không cần cấu hình phức tạp
4. **Linh hoạt**: Chuyển đổi mượt mà giữa các chế độ

## Troubleshooting

### Không thể kết nối AP:
- Kiểm tra tên WiFi "ESP8266_SmartHome"
- Đảm bảo mật khẩu đúng: 12345678
- Thử quên và kết nối lại mạng WiFi

### Không tự động chuyển đổi:
- Kiểm tra Serial Monitor để xem log
- Đảm bảo ESP8266 không bị reset liên tục
- Kiểm tra nguồn điện ổn định

### LED không hoạt động:
- Kiểm tra kết nối chân GPIO2
- Thử thay đổi LED_PIN trong code
- Kiểm tra logic HIGH/LOW phù hợp với LED
