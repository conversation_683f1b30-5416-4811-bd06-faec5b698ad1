#!/bin/bash

# Script chuyển đổi giữa phiên bản đầy đủ và đơn giản
# Sử dụng: ./switch.sh [simple|full]

set -e

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Hàm hiển thị help
show_help() {
    echo -e "${BLUE}🔄 ESP8266 Version Switcher${NC}"
    echo ""
    echo "Sử dụng:"
    echo "  ./switch.sh simple    # <PERSON><PERSON><PERSON><PERSON> sang phiên bản đơn giản (mDNS)"
    echo "  ./switch.sh full      # <PERSON><PERSON><PERSON><PERSON> sang phiên bản đầy đủ (LED + Motor)"
    echo "  ./switch.sh status    # Kiểm tra phiên bản hiện tại"
    echo "  ./switch.sh help      # Hiển thị hướng dẫn này"
    echo ""
    echo "Phiên bản:"
    echo "  📱 simple: Web server đơn giản với mDNS (http://esp.local)"
    echo "  🎛️  full:   Đi<PERSON>u khiển LED + Motor với giao diện đẹp"
}

# Hàm kiểm tra phiên bản hiện tại
check_current_version() {
    if [ -f "src/main.cpp" ]; then
        if grep -q "ESP8266 mDNS Web Server" src/main.cpp; then
            echo -e "${GREEN}📱 Phiên bản hiện tại: SIMPLE (mDNS)${NC}"
            return 0
        elif grep -q "Smart Home Control" src/main.cpp; then
            echo -e "${GREEN}🎛️  Phiên bản hiện tại: FULL (LED + Motor)${NC}"
            return 1
        else
            echo -e "${YELLOW}⚠️  Không xác định được phiên bản hiện tại${NC}"
            return 2
        fi
    else
        echo -e "${RED}❌ Không tìm thấy file src/main.cpp${NC}"
        return 3
    fi
}

# Hàm backup file hiện tại
backup_current() {
    local backup_name=$1
    if [ -f "src/main.cpp" ]; then
        echo -e "${YELLOW}💾 Backup file hiện tại thành ${backup_name}${NC}"
        cp src/main.cpp "src/${backup_name}"
    fi
}

# Hàm chuyển sang phiên bản đơn giản
switch_to_simple() {
    echo -e "${BLUE}🔄 Chuyển sang phiên bản SIMPLE...${NC}"
    
    # Backup phiên bản hiện tại
    backup_current "main_full.cpp"
    
    # Kiểm tra file simple có tồn tại không
    if [ ! -f "src/simple_mdns.cpp" ]; then
        echo -e "${RED}❌ Không tìm thấy file src/simple_mdns.cpp${NC}"
        echo "Vui lòng tạo file này trước khi chuyển đổi."
        exit 1
    fi
    
    # Copy file simple thành main.cpp
    cp src/simple_mdns.cpp src/main.cpp
    
    echo -e "${GREEN}✅ Đã chuyển sang phiên bản SIMPLE${NC}"
    echo -e "${BLUE}📋 Tính năng:${NC}"
    echo "   • Web server đơn giản"
    echo "   • mDNS (truy cập qua http://esp.local)"
    echo "   • Hiển thị thông tin hệ thống"
    echo ""
    echo -e "${YELLOW}📝 Nhớ cấu hình WiFi trong src/main.cpp:${NC}"
    echo "   const char* ssid = \"TEN_WIFI_CUA_BAN\";"
    echo "   const char* password = \"MAT_KHAU_WIFI\";"
    echo ""
    echo -e "${BLUE}🚀 Upload chương trình:${NC}"
    echo "   pio run --target upload"
    echo "   pio device monitor"
}

# Hàm chuyển sang phiên bản đầy đủ
switch_to_full() {
    echo -e "${BLUE}🔄 Chuyển sang phiên bản FULL...${NC}"
    
    # Backup phiên bản hiện tại
    backup_current "simple_mdns.cpp"
    
    # Kiểm tra file full có tồn tại không
    if [ ! -f "src/main_full.cpp" ]; then
        echo -e "${RED}❌ Không tìm thấy file src/main_full.cpp${NC}"
        echo "Đang tạo từ backup..."
        
        # Tìm file backup gần nhất
        if [ -f "src/main_full.cpp.bak" ]; then
            cp src/main_full.cpp.bak src/main_full.cpp
        else
            echo -e "${RED}❌ Không tìm thấy backup của phiên bản full${NC}"
            echo "Vui lòng khôi phục file này từ git hoặc tạo lại."
            exit 1
        fi
    fi
    
    # Copy file full thành main.cpp
    cp src/main_full.cpp src/main.cpp
    
    echo -e "${GREEN}✅ Đã chuyển sang phiên bản FULL${NC}"
    echo -e "${BLUE}📋 Tính năng:${NC}"
    echo "   • Điều khiển LED"
    echo "   • Điều khiển Motor DC"
    echo "   • Giao diện responsive"
    echo "   • PWA support"
    echo "   • AP mode fallback"
    echo "   • Static IP"
    echo ""
    echo -e "${YELLOW}📝 Nhớ cấu hình WiFi trong src/main.cpp:${NC}"
    echo "   const char* ssid = \"TEN_WIFI_CUA_BAN\";"
    echo "   const char* password = \"MAT_KHAU_WIFI\";"
    echo ""
    echo -e "${BLUE}🚀 Upload chương trình:${NC}"
    echo "   pio run --target upload"
    echo "   pio device monitor"
}

# Main script
case "$1" in
    "simple")
        switch_to_simple
        ;;
    "full")
        switch_to_full
        ;;
    "status")
        check_current_version
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        echo -e "${RED}❌ Thiếu tham số${NC}"
        echo ""
        show_help
        exit 1
        ;;
    *)
        echo -e "${RED}❌ Tham số không hợp lệ: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
