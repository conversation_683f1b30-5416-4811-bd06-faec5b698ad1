#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <ESP8266mDNS.h>

// ===== CẤU HÌNH WIFI =====
const char* ssid = "ABC";           // Thay đổi tên WiFi của bạn
const char* password = "88888888";  // Thay đổi mật khẩu WiFi của bạn

// ===== CẤU HÌNH mDNS =====
const char* mdns_name = "esp";      // Truy cập qua http://esp.local

// Tạo web server trên port 80
ESP8266WebServer server(80);

// Hàm tạo trang HTML đơn giản
String getSimpleHTML() {
  String html = "<!DOCTYPE html>";
  html += "<html lang='vi'>";
  html += "<head>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<title>ESP8266 Web Server</title>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }";
  html += ".container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
  html += "h1 { color: #333; text-align: center; margin-bottom: 30px; }";
  html += ".welcome { text-align: center; font-size: 18px; color: #666; margin-bottom: 30px; }";
  html += ".info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }";
  html += ".info h3 { margin-top: 0; color: #2196F3; }";
  html += ".info p { margin: 5px 0; }";
  html += ".status { display: inline-block; padding: 5px 10px; border-radius: 3px; font-weight: bold; }";
  html += ".connected { background: #4CAF50; color: white; }";
  html += ".footer { text-align: center; margin-top: 30px; color: #999; font-size: 14px; }";
  html += "</style>";
  html += "</head>";
  html += "<body>";
  html += "<div class='container'>";
  html += "<h1>🌟 Chào mừng đến với ESP8266</h1>";
  html += "<div class='welcome'>";
  html += "ESP8266 Web Server với mDNS đang hoạt động!";
  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>📡 Thông tin kết nối</h3>";
  html += "<p><strong>Trạng thái WiFi:</strong> <span class='status connected'>Đã kết nối</span></p>";
  html += "<p><strong>SSID:</strong> " + String(ssid) + "</p>";
  html += "<p><strong>Địa chỉ IP:</strong> " + WiFi.localIP().toString() + "</p>";
  html += "<p><strong>mDNS:</strong> http://" + String(mdns_name) + ".local</p>";
  html += "<p><strong>Gateway:</strong> " + WiFi.gatewayIP().toString() + "</p>";
  html += "<p><strong>Cường độ tín hiệu:</strong> " + String(WiFi.RSSI()) + " dBm</p>";
  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>⚡ Thông tin hệ thống</h3>";
  html += "<p><strong>Thời gian hoạt động:</strong> " + String(millis() / 1000) + " giây</p>";
  html += "<p><strong>Bộ nhớ trống:</strong> " + String(ESP.getFreeHeap()) + " bytes</p>";
  html += "<p><strong>Tần số CPU:</strong> " + String(ESP.getCpuFreqMHz()) + " MHz</p>";
  html += "</div>";
  
  html += "<div class='footer'>";
  html += "ESP8266 Simple mDNS Web Server<br>";
  html += "Truy cập qua: <strong>http://" + String(mdns_name) + ".local</strong>";
  html += "</div>";
  html += "</div>";
  html += "</body>";
  html += "</html>";
  
  return html;
}

// Xử lý trang chủ
void handleRoot() {
  server.send(200, "text/html", getSimpleHTML());
}

// Xử lý trang không tìm thấy
void handleNotFound() {
  String message = "Trang không tìm thấy!\n\n";
  message += "URI: " + server.uri() + "\n";
  message += "Method: " + (server.method() == HTTP_GET ? "GET" : "POST") + "\n";
  message += "Arguments: " + String(server.args()) + "\n";
  
  for (uint8_t i = 0; i < server.args(); i++) {
    message += " " + server.argName(i) + ": " + server.arg(i) + "\n";
  }
  
  server.send(404, "text/plain", message);
}

void setup() {
  // Khởi tạo Serial Monitor
  Serial.begin(115200);
  delay(100);
  Serial.println();
  Serial.println("========================================");
  Serial.println("🚀 ESP8266 mDNS Web Server Starting...");
  Serial.println("========================================");
  
  // Kết nối WiFi
  Serial.print("📡 Đang kết nối WiFi: ");
  Serial.println(ssid);
  
  WiFi.begin(ssid, password);
  
  // Chờ kết nối WiFi
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.println("✅ WiFi đã kết nối thành công!");
    Serial.println("========================================");
    Serial.println("📊 THÔNG TIN MẠNG:");
    Serial.print("🌐 Địa chỉ IP: ");
    Serial.println(WiFi.localIP());
    Serial.print("🚪 Gateway: ");
    Serial.println(WiFi.gatewayIP());
    Serial.print("📶 Cường độ tín hiệu: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    Serial.println("========================================");
    
    // Khởi tạo mDNS
    Serial.print("🔧 Đang cấu hình mDNS: ");
    Serial.print(mdns_name);
    Serial.println(".local");
    
    if (MDNS.begin(mdns_name)) {
      Serial.println("✅ mDNS đã khởi tạo thành công!");
      Serial.print("🌐 Truy cập qua: http://");
      Serial.print(mdns_name);
      Serial.println(".local");
      
      // Thêm service HTTP
      MDNS.addService("http", "tcp", 80);
      Serial.println("📡 HTTP service đã được đăng ký");
    } else {
      Serial.println("❌ Lỗi: Không thể khởi tạo mDNS!");
    }
    
    // Cấu hình web server
    server.on("/", handleRoot);
    server.onNotFound(handleNotFound);
    
    // Khởi động web server
    server.begin();
    Serial.println("🌐 Web server đã khởi động!");
    Serial.println("========================================");
    Serial.println("🎉 SẴN SÀNG SỬ DỤNG!");
    Serial.println("Truy cập bằng một trong các cách sau:");
    Serial.print("   • http://");
    Serial.println(WiFi.localIP());
    Serial.print("   • http://");
    Serial.print(mdns_name);
    Serial.println(".local");
    Serial.println("========================================");
    
  } else {
    Serial.println();
    Serial.println("❌ Không thể kết nối WiFi!");
    Serial.println("Vui lòng kiểm tra:");
    Serial.println("  - Tên WiFi (SSID) đúng chưa?");
    Serial.println("  - Mật khẩu WiFi đúng chưa?");
    Serial.println("  - ESP8266 có gần router không?");
    Serial.println("Khởi động lại ESP8266 để thử lại...");
  }
}

void loop() {
  // Xử lý các request từ web server
  server.handleClient();
  
  // Cập nhật mDNS
  MDNS.update();
  
  // Kiểm tra kết nối WiFi định kỳ
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 30000) { // Kiểm tra mỗi 30 giây
    lastCheck = millis();
    
    if (WiFi.status() != WL_CONNECTED) {
      Serial.println("⚠️  Mất kết nối WiFi! Đang thử kết nối lại...");
      WiFi.reconnect();
    }
  }
}
