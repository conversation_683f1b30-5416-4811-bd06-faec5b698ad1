#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <ESP8266mDNS.h>

// ===== CẤU HÌNH WIFI =====
const char* ssid = "ABC";           // Thay đổi tên WiFi của bạn
const char* password = "88888888";  // Thay đổi mật khẩu WiFi của bạn

// ===== CẤU HÌNH ACCESS POINT =====
const char* ap_ssid = "ESP8266-SmartHome";     // Tên WiFi của ESP khi làm AP
const char* ap_password = "12345678";          // Mật khẩu WiFi của ESP (tối thiểu 8 ký tự)

// ===== CẤU HÌNH mDNS =====
const char* mdns_name = "esp";      // Truy cập qua http://esp.local

// ===== BIẾN TRẠNG THÁI =====
bool isWiFiConnected = false;       // Trạng thái kết nối WiFi
bool isAPMode = false;              // Trạng thái chế độ AP

// Tạo web server trên port 80
ESP8266WebServer server(80);

// ===== CẤU HÌNH THIẾT BỊ =====
// Định nghĩa chân LED (có thể thay đổi theo board của bạn)
const int LED_PIN = 2;  // GPIO2 (D4 trên NodeMCU)
bool ledState = false;  // Trạng thái LED

// Định nghĩa chân điều khiển động cơ DC
const int MOTOR_PIN1 = 4;  // GPIO4 (D2 trên NodeMCU) - Điều khiển hướng 1
const int MOTOR_PIN2 = 5;  // GPIO5 (D1 trên NodeMCU) - Điều khiển hướng 2
const int MOTOR_ENABLE = 14; // GPIO14 (D5 trên NodeMCU) - PWM để điều khiển tốc độ
int motorState = 0;  // 0: Dừng, 1: Thuận, -1: Nghịch
int motorSpeed = 255; // Tốc độ động cơ (0-255)

// Khai báo các hàm
void checkWiFiConnection();

// Hàm tạo trang HTML với điều khiển thiết bị
String getHTMLPage() {
  String html = "<!DOCTYPE html>";
  html += "<html lang='vi'>";
  html += "<head>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<title>ESP8266 Smart Control</title>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #0a0a0a; color: #ffffff; }";
  html += ".container { max-width: 800px; margin: 0 auto; background: #1a1a1a; padding: 30px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.5); }";
  html += "h1 { color: #ffffff; text-align: center; margin-bottom: 30px; font-size: 28px; }";
  html += ".welcome { text-align: center; font-size: 16px; color: #b0b0b0; margin-bottom: 30px; }";
  html += ".devices { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }";
  html += ".device-card { background: #2a2a2a; border: 1px solid #404040; border-radius: 12px; padding: 20px; }";
  html += ".device-name { font-size: 18px; font-weight: bold; color: #ffffff; margin-bottom: 15px; display: flex; align-items: center; }";
  html += ".device-icon { margin-right: 10px; }";
  html += ".status-display { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: #1a1a1a; border-radius: 8px; }";
  html += ".status-text { font-weight: bold; text-transform: uppercase; }";
  html += ".status-on { color: #00ff88; }";
  html += ".status-off { color: #808080; }";
  html += ".status-indicator { width: 10px; height: 10px; border-radius: 50%; }";
  html += ".indicator-on { background: #00ff88; box-shadow: 0 0 10px #00ff88; }";
  html += ".indicator-off { background: #808080; }";
  html += ".controls { display: flex; gap: 10px; flex-wrap: wrap; }";
  html += ".btn { flex: 1; min-width: 80px; padding: 12px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: bold; cursor: pointer; transition: all 0.3s; text-transform: uppercase; }";
  html += ".btn-on { background: #00ff88; color: #000; }";
  html += ".btn-on:hover { background: #00e67a; transform: translateY(-2px); }";
  html += ".btn-off { background: #ff4757; color: #fff; }";
  html += ".btn-off:hover { background: #ff3742; transform: translateY(-2px); }";
  html += ".btn-stop { background: #ffa502; color: #000; }";
  html += ".btn-stop:hover { background: #ff9500; transform: translateY(-2px); }";
  html += ".speed-control { margin-top: 15px; }";
  html += ".speed-label { display: block; margin-bottom: 10px; color: #b0b0b0; font-size: 14px; }";
  html += ".speed-slider { width: 100%; height: 6px; border-radius: 3px; background: #404040; outline: none; -webkit-appearance: none; }";
  html += ".speed-slider::-webkit-slider-thumb { appearance: none; width: 18px; height: 18px; border-radius: 50%; background: #3742fa; cursor: pointer; }";
  html += ".info { background: #2a2a2a; border: 1px solid #404040; padding: 20px; border-radius: 12px; }";
  html += ".info h3 { margin-top: 0; color: #00ff88; }";
  html += ".info p { margin: 8px 0; color: #b0b0b0; }";
  html += ".info strong { color: #ffffff; }";
  html += ".footer { text-align: center; margin-top: 30px; color: #808080; font-size: 14px; }";
  html += "@media (max-width: 768px) { .devices { grid-template-columns: 1fr; } .controls { flex-direction: column; } }";
  html += "</style>";
  html += "</head>";
  html += "<body>";
  html += "<div class='container'>";
  html += "<h1>⚡ Chào mừng đến với ESP8266</h1>";
  html += "<div class='welcome'>";
  html += "Smart Home Control với mDNS • Truy cập qua http://" + String(mdns_name) + ".local";
  html += "</div>";

  // Phần điều khiển thiết bị
  html += "<div class='devices'>";

  // Card điều khiển LED
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<span class='device-icon'>💡</span>Đèn LED";
  html += "</div>";
  html += "<div class='status-display'>";
  html += "<span class='status-text " + String(ledState ? "status-on" : "status-off") + "'>" + String(ledState ? "ĐANG BẬT" : "ĐANG TẮT") + "</span>";
  html += "<div class='status-indicator " + String(ledState ? "indicator-on" : "indicator-off") + "'></div>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn btn-on' onclick='controlLED(\"on\")'>BẬT</button>";
  html += "<button class='btn btn-off' onclick='controlLED(\"off\")'>TẮT</button>";
  html += "</div>";
  html += "</div>";

  // Card điều khiển động cơ
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<span class='device-icon'>⚙️</span>Động Cơ DC";
  html += "</div>";
  html += "<div class='status-display'>";
  String motorStatusText = "DỪNG";
  String motorStatusClass = "status-off";
  if (motorState == 1) {
    motorStatusText = "THUẬN";
    motorStatusClass = "status-on";
  } else if (motorState == -1) {
    motorStatusText = "NGHỊCH";
    motorStatusClass = "status-on";
  }
  html += "<span class='status-text " + motorStatusClass + "'>" + motorStatusText + "</span>";
  html += "<div class='status-indicator " + String(motorState != 0 ? "indicator-on" : "indicator-off") + "'></div>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn btn-on' onclick='controlMotor(\"forward\")'>THUẬN</button>";
  html += "<button class='btn btn-off' onclick='controlMotor(\"reverse\")'>NGHỊCH</button>";
  html += "<button class='btn btn-stop' onclick='controlMotor(\"stop\")'>DỪNG</button>";
  html += "</div>";
  html += "<div class='speed-control'>";
  html += "<label class='speed-label'>Tốc độ: <span id='speedValue'>" + String(motorSpeed) + "</span>/255</label>";
  html += "<input type='range' min='0' max='255' value='" + String(motorSpeed) + "' onchange='updateSpeed(this.value)' class='speed-slider'>";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>📡 Thông tin kết nối</h3>";

  if (isWiFiConnected) {
    html += "<p><strong>Chế độ:</strong> <span class='status connected'>WiFi Client</span></p>";
    html += "<p><strong>SSID:</strong> " + String(ssid) + "</p>";
    html += "<p><strong>Địa chỉ IP:</strong> " + WiFi.localIP().toString() + "</p>";
    html += "<p><strong>mDNS:</strong> http://" + String(mdns_name) + ".local</p>";
    html += "<p><strong>Gateway:</strong> " + WiFi.gatewayIP().toString() + "</p>";
    html += "<p><strong>Cường độ tín hiệu:</strong> " + String(WiFi.RSSI()) + " dBm</p>";
  } else if (isAPMode) {
    html += "<p><strong>Chế độ:</strong> <span class='status' style='background: #ffa502; color: #000;'>Access Point</span></p>";
    html += "<p><strong>AP SSID:</strong> " + String(ap_ssid) + "</p>";
    html += "<p><strong>AP IP:</strong> " + WiFi.softAPIP().toString() + "</p>";
    html += "<p><strong>mDNS:</strong> http://" + String(mdns_name) + ".local</p>";
    html += "<p><strong>Clients kết nối:</strong> " + String(WiFi.softAPgetStationNum()) + "</p>";
    html += "<p><strong>Trạng thái:</strong> Chờ kết nối WiFi...</p>";
  }

  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>⚡ Thông tin hệ thống</h3>";
  html += "<p><strong>Thời gian hoạt động:</strong> " + String(millis() / 1000) + " giây</p>";
  html += "<p><strong>Bộ nhớ trống:</strong> " + String(ESP.getFreeHeap()) + " bytes</p>";
  html += "<p><strong>Tần số CPU:</strong> " + String(ESP.getCpuFreqMHz()) + " MHz</p>";
  html += "</div>";
  
  html += "<div class='footer'>";
  html += "ESP8266 Smart Control với mDNS<br>";
  html += "Truy cập qua: <strong>http://" + String(mdns_name) + ".local</strong>";
  html += "</div>";
  html += "</div>";

  // JavaScript
  html += "<script>";
  html += "function controlLED(action) {";
  html += "  fetch('/' + action)";
  html += "    .then(response => response.text())";
  html += "    .then(data => { setTimeout(() => location.reload(), 500); })";
  html += "    .catch(error => { console.error('Lỗi:', error); alert('Không thể kết nối!'); });";
  html += "}";
  html += "function controlMotor(action) {";
  html += "  fetch('/motor/' + action)";
  html += "    .then(response => response.text())";
  html += "    .then(data => { setTimeout(() => location.reload(), 500); })";
  html += "    .catch(error => { console.error('Lỗi:', error); alert('Không thể kết nối!'); });";
  html += "}";
  html += "function updateSpeed(value) {";
  html += "  document.getElementById('speedValue').textContent = value;";
  html += "  fetch('/motor/speed?value=' + value)";
  html += "    .then(response => response.text())";
  html += "    .catch(error => console.error('Lỗi:', error));";
  html += "}";
  html += "</script>";
  html += "</body>";
  html += "</html>";

  return html;
}

// Xử lý trang chủ
void handleRoot() {
  server.send(200, "text/html", getHTMLPage());
}

// Xử lý bật LED
void handleLEDOn() {
  ledState = true;
  digitalWrite(LED_PIN, HIGH);
  Serial.println("LED đã được BẬT");
  server.send(200, "text/plain", "LED ON");
}

// Xử lý tắt LED
void handleLEDOff() {
  ledState = false;
  digitalWrite(LED_PIN, LOW);
  Serial.println("LED đã được TẮT");
  server.send(200, "text/plain", "LED OFF");
}

// Xử lý điều khiển động cơ quay thuận
void handleMotorForward() {
  motorState = 1;
  digitalWrite(MOTOR_PIN1, HIGH);
  digitalWrite(MOTOR_PIN2, LOW);
  analogWrite(MOTOR_ENABLE, motorSpeed);
  Serial.println("Động cơ quay THUẬN với tốc độ: " + String(motorSpeed));
  server.send(200, "text/plain", "MOTOR FORWARD");
}

// Xử lý điều khiển động cơ quay nghịch
void handleMotorReverse() {
  motorState = -1;
  digitalWrite(MOTOR_PIN1, LOW);
  digitalWrite(MOTOR_PIN2, HIGH);
  analogWrite(MOTOR_ENABLE, motorSpeed);
  Serial.println("Động cơ quay NGHỊCH với tốc độ: " + String(motorSpeed));
  server.send(200, "text/plain", "MOTOR REVERSE");
}

// Xử lý dừng động cơ
void handleMotorStop() {
  motorState = 0;
  digitalWrite(MOTOR_PIN1, LOW);
  digitalWrite(MOTOR_PIN2, LOW);
  analogWrite(MOTOR_ENABLE, 0);
  Serial.println("Động cơ đã DỪNG");
  server.send(200, "text/plain", "MOTOR STOP");
}

// Xử lý thay đổi tốc độ động cơ
void handleMotorSpeed() {
  if (server.hasArg("value")) {
    int newSpeed = server.arg("value").toInt();
    if (newSpeed >= 0 && newSpeed <= 255) {
      motorSpeed = newSpeed;
      if (motorState != 0) {
        analogWrite(MOTOR_ENABLE, motorSpeed);
      }
      Serial.println("Tốc độ động cơ đã thay đổi: " + String(motorSpeed));
      server.send(200, "text/plain", "SPEED UPDATED");
    } else {
      server.send(400, "text/plain", "INVALID SPEED VALUE");
    }
  } else {
    server.send(400, "text/plain", "MISSING SPEED VALUE");
  }
}

// Xử lý trang không tìm thấy
void handleNotFound() {
  String message = "Trang không tìm thấy!\n\n";
  message += "URI: " + server.uri() + "\n";
  message += "Method: " + String(server.method()) + "\n";
  message += "Arguments: " + String(server.args()) + "\n";

  for (uint8_t i = 0; i < server.args(); i++) {
    message += " " + server.argName(i) + ": " + server.arg(i) + "\n";
  }

  server.send(404, "text/plain", message);
}

void setup() {
  // Khởi tạo Serial Monitor
  Serial.begin(115200);
  delay(100);
  Serial.println();
  Serial.println("========================================");
  Serial.println("🚀 ESP8266 Smart Control với mDNS");
  Serial.println("========================================");

  // Cấu hình chân LED
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW); // Tắt LED ban đầu

  // Cấu hình chân động cơ DC
  pinMode(MOTOR_PIN1, OUTPUT);
  pinMode(MOTOR_PIN2, OUTPUT);
  pinMode(MOTOR_ENABLE, OUTPUT);
  digitalWrite(MOTOR_PIN1, LOW);
  digitalWrite(MOTOR_PIN2, LOW);
  analogWrite(MOTOR_ENABLE, 0); // Dừng động cơ ban đầu

  Serial.println("✅ Đã khởi tạo các chân GPIO");

  // Thử kết nối WiFi
  Serial.print("📡 Đang thử kết nối WiFi: ");
  Serial.println(ssid);

  WiFi.mode(WIFI_STA);  // Chế độ Station (client)
  WiFi.begin(ssid, password);

  // Chờ kết nối WiFi trong 20 giây
  int attempts = 0;
  Serial.print("⏳ Đang kết nối");
  while (WiFi.status() != WL_CONNECTED && attempts < 40) {
    delay(500);
    Serial.print(".");
    attempts++;

    // Hiển thị tiến trình
    if (attempts % 10 == 0) {
      Serial.println();
      Serial.print("🔄 Thử lần " + String(attempts/10) + "/4");
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    // Kết nối WiFi thành công
    isWiFiConnected = true;
    Serial.println();
    Serial.println("✅ WiFi đã kết nối thành công!");
    Serial.println("========================================");
    Serial.println("📊 THÔNG TIN MẠNG:");
    Serial.print("🌐 Địa chỉ IP: ");
    Serial.println(WiFi.localIP());
    Serial.print("🚪 Gateway: ");
    Serial.println(WiFi.gatewayIP());
    Serial.print("📶 Cường độ tín hiệu: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    Serial.println("========================================");

    // Khởi tạo mDNS
    Serial.print("🔧 Đang cấu hình mDNS: ");
    Serial.print(mdns_name);
    Serial.println(".local");

    if (MDNS.begin(mdns_name)) {
      Serial.println("✅ mDNS đã khởi tạo thành công!");
      Serial.print("🌐 Truy cập qua: http://");
      Serial.print(mdns_name);
      Serial.println(".local");

      // Thêm service HTTP
      MDNS.addService("http", "tcp", 80);
      Serial.println("📡 HTTP service đã được đăng ký");
    } else {
      Serial.println("❌ Lỗi: Không thể khởi tạo mDNS!");
    }

  } else {
    // Không thể kết nối WiFi, chuyển sang chế độ AP
    Serial.println();
    Serial.println("❌ KHÔNG THỂ KẾT NỐI WIFI!");
    Serial.println("🔧 Nguyên nhân có thể:");
    Serial.println("   - Sai tên WiFi hoặc mật khẩu");
    Serial.println("   - WiFi không khả dụng");
    Serial.println("   - ESP8266 quá xa router");
    Serial.println();
    Serial.println("🔄 Chuyển sang chế độ Access Point...");

    // Chuyển sang chế độ AP
    WiFi.mode(WIFI_AP);
    WiFi.softAP(ap_ssid, ap_password);
    isAPMode = true;

    Serial.println("✅ Access Point đã được tạo!");
    Serial.println("========================================");
    Serial.println("📶 THÔNG TIN ACCESS POINT:");
    Serial.print("🏷️  SSID: ");
    Serial.println(ap_ssid);
    Serial.print("🔐 Password: ");
    Serial.println(ap_password);
    Serial.print("🌐 IP Address: ");
    Serial.println(WiFi.softAPIP());

    // Khởi tạo mDNS cho AP mode
    Serial.print("🔧 Đang cấu hình mDNS cho AP: ");
    Serial.print(mdns_name);
    Serial.println(".local");

    if (MDNS.begin(mdns_name)) {
      Serial.println("✅ mDNS đã khởi tạo thành công cho AP mode!");
      Serial.print("🌐 Truy cập qua: http://");
      Serial.print(mdns_name);
      Serial.println(".local");

      // Thêm service HTTP
      MDNS.addService("http", "tcp", 80);
      Serial.println("📡 HTTP service đã được đăng ký cho AP");
    } else {
      Serial.println("❌ Lỗi: Không thể khởi tạo mDNS cho AP mode!");
    }

    Serial.println("========================================");
    Serial.println("📱 HƯỚNG DẪN KẾT NỐI:");
    Serial.println("   1. Kết nối điện thoại/máy tính vào WiFi: " + String(ap_ssid));
    Serial.println("   2. Nhập mật khẩu: " + String(ap_password));
    Serial.println("   3. Truy cập bằng một trong các cách:");
    Serial.println("      • http://" + WiFi.softAPIP().toString());
    Serial.println("      • http://" + String(mdns_name) + ".local");
    Serial.println("   4. ESP8266 sẽ tự động thử kết nối WiFi lại sau 30 giây");
    Serial.println("========================================");
  }

  // Cấu hình web server (cho cả WiFi và AP mode)
  server.on("/", handleRoot);
  server.on("/on", handleLEDOn);
  server.on("/off", handleLEDOff);
  server.on("/motor/forward", handleMotorForward);
  server.on("/motor/reverse", handleMotorReverse);
  server.on("/motor/stop", handleMotorStop);
  server.on("/motor/speed", handleMotorSpeed);
  server.onNotFound(handleNotFound);

  // Khởi động web server
  server.begin();
  Serial.println("🌐 Web server đã khởi động!");

  if (isWiFiConnected) {
    Serial.println("========================================");
    Serial.println("🎉 SẴN SÀNG SỬ DỤNG!");
    Serial.println("Truy cập bằng một trong các cách sau:");
    Serial.print("   • http://");
    Serial.println(WiFi.localIP());
    Serial.print("   • http://");
    Serial.print(mdns_name);
    Serial.println(".local");
    Serial.println("========================================");
  } else if (isAPMode) {
    Serial.println("========================================");
    Serial.println("🎉 ACCESS POINT SẴN SÀNG!");
    Serial.println("Truy cập qua:");
    Serial.print("   • http://");
    Serial.println(WiFi.softAPIP());
    Serial.print("   • http://");
    Serial.print(mdns_name);
    Serial.println(".local");
    Serial.println("========================================");
  }
}

void loop() {
  // Xử lý các request từ web server
  server.handleClient();

  // Cập nhật mDNS (hoạt động trong cả WiFi và AP mode)
  MDNS.update();

  // Kiểm tra và quản lý kết nối WiFi định kỳ
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 30000) { // Kiểm tra mỗi 30 giây
    lastCheck = millis();
    checkWiFiConnection();
  }
}

// Hàm kiểm tra và quản lý kết nối WiFi
void checkWiFiConnection() {
  if (isWiFiConnected && WiFi.status() != WL_CONNECTED) {
    // Mất kết nối WiFi, chuyển sang AP mode
    Serial.println("⚠️  Mất kết nối WiFi! Chuyển sang chế độ Access Point...");
    isWiFiConnected = false;
    isAPMode = true;

    WiFi.mode(WIFI_AP);
    WiFi.softAP(ap_ssid, ap_password);

    // Khởi tạo mDNS cho AP mode
    if (MDNS.begin(mdns_name)) {
      MDNS.addService("http", "tcp", 80);
      Serial.println("✅ Access Point + mDNS đã được kích hoạt!");
      Serial.println("📶 Kết nối vào WiFi: " + String(ap_ssid));
      Serial.println("🌐 Truy cập: http://" + WiFi.softAPIP().toString());
      Serial.println("🔧 Hoặc qua mDNS: http://" + String(mdns_name) + ".local");
    } else {
      Serial.println("✅ Access Point đã được kích hoạt!");
      Serial.println("📶 Kết nối vào WiFi: " + String(ap_ssid));
      Serial.println("🌐 Truy cập: http://" + WiFi.softAPIP().toString());
    }
  }
  else if (isAPMode && !isWiFiConnected) {
    // Đang ở chế độ AP, thử kết nối lại WiFi
    Serial.println("🔄 Thử kết nối lại WiFi từ chế độ AP...");
    WiFi.mode(WIFI_STA);
    WiFi.begin(ssid, password);

    // Chờ 10 giây để kết nối
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
      delay(500);
      Serial.print(".");
      attempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
      // Kết nối WiFi thành công, thoát khỏi AP mode
      isWiFiConnected = true;
      isAPMode = false;
      Serial.println();
      Serial.println("✅ Đã kết nối lại WiFi thành công!");
      Serial.println("🌐 IP Address: " + WiFi.localIP().toString());

      // Khởi tạo lại mDNS
      if (MDNS.begin(mdns_name)) {
        Serial.println("✅ mDNS đã được khởi tạo lại!");
        MDNS.addService("http", "tcp", 80);
      }
    } else {
      // Vẫn không kết nối được, quay lại AP mode
      Serial.println();
      Serial.println("❌ Không thể kết nối WiFi, tiếp tục chế độ AP");
      WiFi.mode(WIFI_AP);
      WiFi.softAP(ap_ssid, ap_password);
    }
  }
}
