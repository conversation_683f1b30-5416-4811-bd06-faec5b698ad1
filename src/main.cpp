#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>

// Thông tin WiFi - Thay đổi theo mạng WiFi của bạn
const char* ssid = "ABC";     // Tên WiFi
const char* password = "88888888"; // Mật khẩu WiFi

// Cấu hình IP tĩnh (Static IP) - Thay đổi theo mạng của bạn
// ⚠️  QUAN TRỌNG: Thay đổi theo mạng WiFi của bạn!
// 💡 Cách tìm: Mở cmd → gõ "ipconfig" → xem Default Gateway
IPAddress local_IP(192, 168, 1, 100);      // IP cố định cho ESP8266 (thay đổi số cuối)
IPAddress gateway(192, 168, 1, 1);         // Gateway = IP router (thường .1)
IPAddress subnet(255, 255, 255, 0);        // Subnet mask (thường như này)
IPAddress primaryDNS(8, 8, 8, 8);          // DNS chính (Google DNS)
IPAddress secondaryDNS(8, 8, 4, 4);        // DNS phụ (Google DNS)

// 🔧 Nếu mạng khác, thử các cấu hình phổ biến:
// Mạng 192.168.0.x: local_IP(192,168,0,100), gateway(192,168,0,1)
// Mạng 10.0.0.x: local_IP(10,0,0,100), gateway(10,0,0,1)

// Thông tin Access Point (khi không có WiFi)
const char* ap_ssid = "ESP8266";     // Tên WiFi của ESP
const char* ap_password = "12345678";          // Mật khẩu WiFi của ESP (tối thiểu 8 ký tự)

// Tạo web server trên port 80
ESP8266WebServer server(80);

// Biến trạng thái kết nối
bool isWiFiConnected = false;
bool isAPMode = false;
bool useStaticIP = true;  // Có sử dụng IP tĩnh không

// Định nghĩa chân LED (có thể thay đổi theo board của bạn)
const int LED_PIN = 2;  // GPIO2 (D4 trên NodeMCU)
bool ledState = false;  // Trạng thái LED

// Định nghĩa chân điều khiển động cơ DC
const int MOTOR_PIN1 = 4;  // GPIO4 (D2 trên NodeMCU) - Điều khiển hướng 1
const int MOTOR_PIN2 = 5;  // GPIO5 (D1 trên NodeMCU) - Điều khiển hướng 2
const int MOTOR_ENABLE = 14; // GPIO14 (D5 trên NodeMCU) - PWM để điều khiển tốc độ
int motorState = 0;  // 0: Dừng, 1: Thuận, -1: Nghịch
int motorSpeed = 255; // Tốc độ động cơ (0-255)

// Khai báo các hàm handler
void handleRoot();
void handleLEDOn();
void handleLEDOff();
void handleMotorForward();
void handleMotorReverse();
void handleMotorStop();
void handleMotorSpeed();
void handleStatus();
void handleManifest();
void handleServiceWorker();
void checkWiFiConnection();
void printNetworkInfo();
String getHTMLPage();

// Hàm tạo HTML cho ứng dụng mobile
String getHTMLPage() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<meta charset='UTF-8'>";
  html += "<title>Smart Home Control</title>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover'>";
  html += "<meta name='apple-mobile-web-app-capable' content='yes'>";
  html += "<meta name='apple-mobile-web-app-status-bar-style' content='black-translucent'>";
  html += "<meta name='theme-color' content='#0a0a0a'>";
  html += "<meta name='screen-orientation' content='natural'>";
  html += "<link rel='manifest' href='/manifest.json'>";
  html += "<style>";
  html += ":root { --bg-primary: #0a0a0a; --bg-secondary: #1a1a1a; --bg-card: #2a2a2a; --bg-card-hover: #333333; --text-primary: #ffffff; --text-secondary: #b0b0b0; --text-muted: #808080; --accent-green: #00ff88; --accent-red: #ff4757; --accent-orange: #ffa502; --accent-blue: #3742fa; --border-color: #404040; --shadow-dark: rgba(0,0,0,0.5); }";
  html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
  html += "body { font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: var(--bg-primary); color: var(--text-primary); min-height: 100vh; min-height: 100dvh; padding: 10px; margin: 0; overflow-x: hidden; }";
  html += ".container { display: flex; align-items: flex-start; justify-content: center; min-height: calc(100vh - 20px); min-height: calc(100dvh - 20px); width: 100%; }";
  html += ".app { width: 100%; max-width: 1200px; background: var(--bg-secondary); border: 1px solid var(--border-color); border-radius: 16px; padding: 20px; box-shadow: 0 8px 32px var(--shadow-dark); margin-top: 60px; }";
  html += ".header { text-align: center; margin-bottom: 25px; border-bottom: 1px solid var(--border-color); padding-bottom: 20px; }";
  html += ".header h1 { color: var(--text-primary); font-size: 28px; font-weight: 700; margin-bottom: 8px; letter-spacing: -0.5px; }";
  html += ".header p { color: var(--text-secondary); font-size: 14px; font-weight: 400; }";
  html += ".devices-grid { display: grid; gap: 20px; margin-bottom: 20px; }";
  html += ".device-card { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: 12px; padding: 20px; transition: all 0.3s ease; }";
  html += ".device-card:hover { background: var(--bg-card-hover); border-color: var(--text-muted); transform: translateY(-2px); box-shadow: 0 8px 24px var(--shadow-dark); }";
  html += ".device-name { font-size: 18px; font-weight: 600; color: var(--text-primary); margin-bottom: 16px; display: flex; align-items: center; }";
  html += ".device-icon { width: 20px; height: 20px; margin-right: 12px; opacity: 0.8; }";
  html += ".status-display { display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px; padding: 12px; background: var(--bg-secondary); border-radius: 8px; border: 1px solid var(--border-color); }";
  html += ".status-text { font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; }";
  html += ".status-text.on { color: var(--accent-green); }";
  html += ".status-text.off { color: var(--text-muted); }";
  html += ".status-indicator { width: 10px; height: 10px; border-radius: 50%; transition: all 0.3s ease; }";
  html += ".status-indicator.on { background: var(--accent-green); box-shadow: 0 0 12px var(--accent-green); animation: pulse 2s infinite; }";
  html += ".status-indicator.off { background: var(--text-muted); }";
  html += "@keyframes pulse { 0%, 100% { opacity: 1; transform: scale(1); } 50% { opacity: 0.7; transform: scale(1.1); } }";
  html += ".controls { display: flex; gap: 12px; flex-wrap: wrap; }";
  html += ".btn { flex: 1; min-width: 90px; padding: 14px 16px; border: 2px solid transparent; border-radius: 8px; font-size: 13px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; text-transform: uppercase; letter-spacing: 0.5px; position: relative; overflow: hidden; }";
  html += ".btn:active { transform: scale(0.96); }";
  html += ".btn:before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: rgba(255,255,255,0.1); transition: left 0.3s ease; }";
  html += ".btn:hover:before { left: 100%; }";
  html += ".btn-on { background: var(--accent-green); color: var(--bg-primary); border-color: var(--accent-green); }";
  html += ".btn-on:hover { background: #00e67a; box-shadow: 0 4px 16px rgba(0,255,136,0.3); }";
  html += ".btn-off { background: var(--accent-red); color: white; border-color: var(--accent-red); }";
  html += ".btn-off:hover { background: #ff3742; box-shadow: 0 4px 16px rgba(255,71,87,0.3); }";
  html += ".btn-stop { background: var(--accent-orange); color: var(--bg-primary); border-color: var(--accent-orange); }";
  html += ".btn-stop:hover { background: #ff9500; box-shadow: 0 4px 16px rgba(255,165,2,0.3); }";
  html += ".speed-control { margin-top: 16px; padding: 16px; background: var(--bg-secondary); border-radius: 8px; border: 1px solid var(--border-color); }";
  html += ".speed-label { display: block; margin-bottom: 12px; color: var(--text-secondary); font-size: 13px; font-weight: 500; }";
  html += ".speed-slider { width: 100%; height: 8px; border-radius: 4px; background: var(--border-color); outline: none; -webkit-appearance: none; cursor: pointer; }";
  html += ".speed-slider::-webkit-slider-thumb { appearance: none; width: 20px; height: 20px; border-radius: 50%; background: var(--accent-blue); cursor: pointer; box-shadow: 0 2px 8px rgba(55,66,250,0.4); }";
  html += ".speed-slider::-moz-range-thumb { width: 20px; height: 20px; border-radius: 50%; background: var(--accent-blue); cursor: pointer; border: none; box-shadow: 0 2px 8px rgba(55,66,250,0.4); }";
  html += ".info-panel { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: 12px; padding: 16px; }";
  html += ".info-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; padding: 8px 0; }";
  html += ".info-row:not(:last-child) { border-bottom: 1px solid var(--border-color); }";
  html += ".info-row:last-child { margin-bottom: 0; }";
  html += ".info-label { color: var(--text-secondary); font-size: 13px; font-weight: 500; }";
  html += ".info-value { color: var(--text-primary); font-size: 13px; font-weight: 600; font-family: 'SF Mono', 'Monaco', 'Consolas', monospace; }";
  html += ".loading { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); backdrop-filter: blur(4px); z-index: 1000; align-items: center; justify-content: center; }";
  html += ".spinner { width: 40px; height: 40px; border: 3px solid var(--border-color); border-top: 3px solid var(--accent-blue); border-radius: 50%; animation: spin 1s linear infinite; }";
  html += "@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }";
  html += "/* Menu Hamburger */";
  html += ".menu-toggle { position: fixed; top: 15px; right: 15px; z-index: 1001; background: var(--bg-card); border: 2px solid var(--border-color); border-radius: 12px; padding: 14px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(10px); box-shadow: 0 4px 20px var(--shadow-dark); }";
  html += ".menu-toggle:hover { background: var(--bg-card-hover); border-color: var(--accent-blue); transform: scale(1.05); }";
  html += ".menu-toggle:active { transform: scale(0.95); }";
  html += ".hamburger { width: 22px; height: 16px; position: relative; }";
  html += ".hamburger span { display: block; position: absolute; height: 2px; width: 100%; background: var(--text-primary); border-radius: 2px; opacity: 1; left: 0; transform: rotate(0deg); transition: .3s ease-in-out; }";
  html += ".hamburger span:nth-child(1) { top: 0px; }";
  html += ".hamburger span:nth-child(2) { top: 7px; }";
  html += ".hamburger span:nth-child(3) { top: 14px; }";
  html += ".menu-toggle.active .hamburger span:nth-child(1) { top: 7px; transform: rotate(135deg); background: var(--accent-red); }";
  html += ".menu-toggle.active .hamburger span:nth-child(2) { opacity: 0; left: -60px; }";
  html += ".menu-toggle.active .hamburger span:nth-child(3) { top: 7px; transform: rotate(-135deg); background: var(--accent-red); }";
  html += "/* Sidebar */";
  html += ".sidebar { position: fixed; top: 0; right: -100%; width: 100%; max-width: 380px; height: 100vh; height: 100dvh; background: var(--bg-secondary); border-left: 2px solid var(--border-color); z-index: 1000; transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1); overflow-y: auto; box-shadow: -8px 0 32px var(--shadow-dark); }";
  html += ".sidebar.active { right: 0; }";
  html += ".sidebar-header { padding: 20px; border-bottom: 1px solid var(--border-color); }";
  html += ".sidebar-title { color: var(--text-primary); font-size: 20px; font-weight: 600; margin-bottom: 8px; }";
  html += ".sidebar-subtitle { color: var(--text-secondary); font-size: 14px; }";
  html += ".sidebar-content { padding: 20px; }";
  html += ".sidebar-section { margin-bottom: 24px; }";
  html += ".section-title { color: var(--text-primary); font-size: 16px; font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; }";
  html += ".section-icon { width: 18px; height: 18px; margin-right: 8px; }";
  html += ".info-grid { display: grid; gap: 12px; }";
  html += ".info-item { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: 8px; padding: 12px; }";
  html += ".info-item-label { color: var(--text-secondary); font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px; }";
  html += ".info-item-value { color: var(--text-primary); font-size: 14px; font-weight: 600; font-family: 'SF Mono', 'Monaco', 'Consolas', monospace; }";
  html += ".setting-item { background: var(--bg-card); border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; margin-bottom: 12px; }";
  html += ".setting-label { color: var(--text-primary); font-size: 14px; font-weight: 500; margin-bottom: 8px; }";
  html += ".setting-description { color: var(--text-secondary); font-size: 12px; margin-bottom: 12px; }";
  html += ".toggle-switch { position: relative; display: inline-block; width: 50px; height: 24px; }";
  html += ".toggle-switch input { opacity: 0; width: 0; height: 0; }";
  html += ".slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: var(--border-color); transition: .4s; border-radius: 24px; }";
  html += ".slider:before { position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background: white; transition: .4s; border-radius: 50%; }";
  html += "input:checked + .slider { background: var(--accent-blue); }";
  html += "input:checked + .slider:before { transform: translateX(26px); }";
  html += ".overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 999; }";
  html += ".overlay.active { display: block; }";
  html += "/* Base Mobile Styles */";
  html += "@media (max-width: 768px) {";
  html += ".app { margin: 8px; padding: 16px; margin-top: 70px; }";
  html += ".menu-toggle { top: 12px; right: 12px; padding: 12px; }";
  html += ".hamburger { width: 20px; height: 14px; }";
  html += ".hamburger span:nth-child(2) { top: 6px; }";
  html += ".hamburger span:nth-child(3) { top: 12px; }";
  html += ".menu-toggle.active .hamburger span:nth-child(1) { top: 6px; }";
  html += ".menu-toggle.active .hamburger span:nth-child(3) { top: 6px; }";
  html += ".sidebar { width: 100%; right: -100%; }";
  html += "}";
  html += "/* Mobile Portrait */";
  html += "@media (max-width: 768px) and (orientation: portrait) {";
  html += ".devices-grid { grid-template-columns: 1fr; gap: 16px; }";
  html += ".header h1 { font-size: 24px; }";
  html += ".header { padding-bottom: 16px; margin-bottom: 20px; }";
  html += ".device-card { padding: 18px; }";
  html += ".btn { padding: 16px 12px; font-size: 14px; min-width: 100px; }";
  html += ".device-name { font-size: 17px; }";
  html += ".status-display { padding: 12px; }";
  html += ".container { padding: 0 8px; }";
  html += "}";
  html += "/* Mobile Landscape */";
  html += "@media (max-width: 768px) and (orientation: landscape) {";
  html += ".devices-grid { grid-template-columns: 1fr 1fr; gap: 12px; }";
  html += ".app { padding: 12px; margin: 4px; margin-top: 60px; }";
  html += ".container { min-height: calc(100vh - 8px); min-height: calc(100dvh - 8px); padding: 0 4px; }";
  html += ".header { margin-bottom: 8px; padding-bottom: 8px; }";
  html += ".header h1 { font-size: 18px; }";
  html += ".header p { font-size: 12px; }";
  html += ".device-card { padding: 12px; }";
  html += ".device-name { font-size: 15px; margin-bottom: 10px; }";
  html += ".btn { padding: 10px 8px; font-size: 11px; min-width: 70px; }";
  html += ".controls { gap: 6px; }";
  html += ".speed-control { margin-top: 8px; padding: 10px; }";
  html += ".speed-label { font-size: 11px; margin-bottom: 6px; }";
  html += ".status-display { padding: 8px; margin-bottom: 10px; }";
  html += ".status-text { font-size: 12px; }";
  html += ".sidebar { width: 320px; right: -320px; }";
  html += ".menu-toggle { top: 8px; right: 8px; padding: 10px; }";
  html += "body { padding: 4px; }";
  html += "}";
  html += "/* Tablet Portrait */";
  html += "@media (min-width: 769px) and (max-width: 1023px) and (orientation: portrait) {";
  html += ".devices-grid { grid-template-columns: 1fr 1fr; gap: 20px; }";
  html += ".app { padding: 24px; margin-top: 70px; }";
  html += ".btn { padding: 14px 12px; font-size: 14px; }";
  html += ".sidebar { width: 400px; right: -400px; }";
  html += "}";
  html += "/* Tablet Landscape */";
  html += "@media (min-width: 769px) and (max-width: 1023px) and (orientation: landscape) {";
  html += ".devices-grid { grid-template-columns: 1fr 1fr 1fr; gap: 16px; }";
  html += ".app { padding: 20px; margin-top: 65px; }";
  html += ".header h1 { font-size: 22px; }";
  html += ".btn { padding: 12px 10px; font-size: 13px; }";
  html += ".sidebar { width: 350px; right: -350px; }";
  html += "}";
  html += "/* Desktop */";
  html += "@media (min-width: 1024px) {";
  html += ".devices-grid { grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); max-width: 1000px; gap: 24px; }";
  html += ".app { padding: 32px; max-width: 1200px; margin-top: 80px; }";
  html += ".btn { padding: 16px 20px; font-size: 14px; }";
  html += ".sidebar { width: 380px; right: -380px; }";
  html += ".menu-toggle { top: 20px; right: 20px; }";
  html += "}";
  html += "/* Force landscape orientation support */";
  html += "@media screen and (max-height: 500px) {";
  html += ".app { margin-top: 50px; padding: 10px; }";
  html += ".header { margin-bottom: 8px; padding-bottom: 8px; }";
  html += ".header h1 { font-size: 16px; }";
  html += ".device-card { padding: 10px; }";
  html += ".btn { padding: 8px 6px; font-size: 10px; }";
  html += ".menu-toggle { top: 6px; right: 6px; padding: 8px; }";
  html += "}";
  html += "</style></head><body>";
  html += "<!-- Menu Toggle Button -->";
  html += "<div class='menu-toggle' onclick='toggleMenu()'>";
  html += "<div class='hamburger'>";
  html += "<span></span>";
  html += "<span></span>";
  html += "<span></span>";
  html += "</div>";
  html += "</div>";
  html += "<!-- Overlay -->";
  html += "<div class='overlay' onclick='closeMenu()'></div>";
  html += "<!-- Sidebar -->";
  html += "<div class='sidebar' id='sidebar'>";
  html += "<div class='sidebar-header'>";
  html += "<div class='sidebar-title'>⚙️ Cài Đặt</div>";
  html += "<div class='sidebar-subtitle'>ESP8266 Smart Home</div>";
  html += "</div>";
  html += "<div class='sidebar-content'>";
  html += "<div class='sidebar-section'>";
  html += "<div class='section-title'>";
  html += "<svg class='section-icon' viewBox='0 0 24 24' fill='#3742fa'><path d='M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z'/></svg>";
  html += "Thông Tin Hệ Thống";
  html += "</div>";
  html += "<div class='info-grid'>";
  html += "<div class='info-item'>";
  html += "<div class='info-item-label'>IP Address</div>";
  if (isWiFiConnected) {
    html += "<div class='info-item-value'>" + WiFi.localIP().toString() + "</div>";
  } else {
    html += "<div class='info-item-value'>" + WiFi.softAPIP().toString() + "</div>";
  }
  html += "</div>";
  html += "<div class='info-item'>";
  html += "<div class='info-item-label'>Chế Độ</div>";
  if (isWiFiConnected) {
    html += "<div class='info-item-value'>WiFi: " + String(ssid) + "</div>";
  } else {
    html += "<div class='info-item-value'>AP: " + String(ap_ssid) + "</div>";
  }
  html += "</div>";
  html += "<div class='info-item'>";
  html += "<div class='info-item-label'>Uptime</div>";
  html += "<div class='info-item-value'>" + String(millis() / 1000) + "s</div>";
  html += "</div>";
  html += "<div class='info-item'>";
  html += "<div class='info-item-label'>Free Memory</div>";
  html += "<div class='info-item-value'>" + String(ESP.getFreeHeap()) + " bytes</div>";
  html += "</div>";
  if (isWiFiConnected) {
    html += "<div class='info-item'>";
    html += "<div class='info-item-label'>Gateway</div>";
    html += "<div class='info-item-value'>" + WiFi.gatewayIP().toString() + "</div>";
    html += "</div>";
    html += "<div class='info-item'>";
    html += "<div class='info-item-label'>Subnet</div>";
    html += "<div class='info-item-value'>" + WiFi.subnetMask().toString() + "</div>";
    html += "</div>";
    html += "<div class='info-item'>";
    html += "<div class='info-item-label'>DNS</div>";
    html += "<div class='info-item-value'>" + WiFi.dnsIP().toString() + "</div>";
    html += "</div>";
  }
  html += "</div>";
  html += "</div>";
  html += "<div class='sidebar-section'>";
  html += "<div class='section-title'>";
  html += "<svg class='section-icon' viewBox='0 0 24 24' fill='#00ff88'><path d='M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z'/></svg>";
  html += "Cài Đặt";
  html += "</div>";
  html += "<div class='setting-item'>";
  html += "<div class='setting-label'>Auto Refresh</div>";
  html += "<div class='setting-description'>Tự động làm mới giao diện mỗi 30 giây</div>";
  html += "<label class='toggle-switch'>";
  html += "<input type='checkbox' checked onchange='toggleAutoRefresh(this)'>";
  html += "<span class='slider'></span>";
  html += "</label>";
  html += "</div>";
  html += "<div class='setting-item'>";
  html += "<div class='setting-label'>Haptic Feedback</div>";
  html += "<div class='setting-description'>Rung nhẹ khi nhấn nút (chỉ mobile)</div>";
  html += "<label class='toggle-switch'>";
  html += "<input type='checkbox' checked onchange='toggleHaptic(this)'>";
  html += "<span class='slider'></span>";
  html += "</label>";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  html += "<div class='container'>";
  html += "<div class='app'>";
  html += "<div class='header'>";
  html += "<h1>⚡ Smart Home</h1>";
  html += "<p>ESP8266 Control Panel • Dark Mode</p>";
  html += "</div>";
  html += "<div class='devices-grid'>";
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<svg class='device-icon' viewBox='0 0 24 24' fill='#00ff88'><path d='M12,2A7,7 0 0,0 5,9C5,11.38 6.19,13.47 8,14.74V17A1,1 0 0,0 9,18H15A1,1 0 0,0 16,17V14.74C17.81,13.47 19,11.38 19,9A7,7 0 0,0 12,2M9,21V20H15V21A1,1 0 0,1 14,22H10A1,1 0 0,1 9,21M12,4A5,5 0 0,1 17,9C17,10.64 16.05,12.08 14.66,12.93L14,13.31V16H10V13.31L9.34,12.93C7.95,12.08 7,10.64 7,9A5,5 0 0,1 12,4Z'/></svg>";
  html += "Đèn LED";
  html += "</div>";
  html += "<div class='status-display'>";
  html += "<span class='status-text " + String(ledState ? "on" : "off") + "'>" + String(ledState ? "DANG BAT" : "DANG TAT") + "</span>";
  html += "<div class='status-indicator " + String(ledState ? "on" : "off") + "'></div>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn btn-on' onclick='controlLED(\"on\")'>● BẬT</button>";
  html += "<button class='btn btn-off' onclick='controlLED(\"off\")'>○ TẮT</button>";
  html += "</div>";
  html += "</div>";

  // Card điều khiển động cơ
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<svg class='device-icon' viewBox='0 0 24 24' fill='#3742fa'><path d='M12,11A1,1 0 0,0 13,12A1,1 0 0,0 12,13A1,1 0 0,0 11,12A1,1 0 0,0 12,11M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z'/></svg>";
  html += "Động Cơ DC";
  html += "</div>";
  html += "<div class='status-display'>";
  String motorStatusText = "DUNG";
  String motorStatusClass = "off";
  if (motorState == 1) {
    motorStatusText = "THUAN";
    motorStatusClass = "on";
  } else if (motorState == -1) {
    motorStatusText = "NGHICH";
    motorStatusClass = "on";
  }
  html += "<span class='status-text " + motorStatusClass + "'>" + motorStatusText + "</span>";
  html += "<div class='status-indicator " + motorStatusClass + "'></div>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn btn-on' onclick='controlMotor(\"forward\")'>▶ THUẬN</button>";
  html += "<button class='btn btn-off' onclick='controlMotor(\"reverse\")'>◀ NGHỊCH</button>";
  html += "<button class='btn btn-stop' onclick='controlMotor(\"stop\")'>■ DỪNG</button>";
  html += "</div>";
  html += "<div class='speed-control'>";
  html += "<label class='speed-label'>⚡ Tốc độ: <span id='speedValue'>" + String(motorSpeed) + "</span>/255</label>";
  html += "<input type='range' min='0' max='255' value='" + String(motorSpeed) + "' onchange='updateSpeed(this.value)' class='speed-slider'>";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  html += "<div class='loading' id='loading'>";
  html += "<div class='spinner'></div>";
  html += "</div>";
  html += "<script>";
  html += "function showLoading() { document.getElementById('loading').style.display = 'flex'; }";
  html += "function hideLoading() { document.getElementById('loading').style.display = 'none'; }";
  html += "function controlLED(action) {";
  html += "showLoading();";
  html += "fetch('/' + action)";
  html += ".then(response => response.text())";
  html += ".then(data => { setTimeout(() => { hideLoading(); location.reload(); }, 500); })";
  html += ".catch(error => { hideLoading(); console.error('Loi:', error); alert('Khong the ket noi den thiet bi!'); });";
  html += "}";
  html += "function controlMotor(action) {";
  html += "showLoading();";
  html += "fetch('/motor/' + action)";
  html += ".then(response => response.text())";
  html += ".then(data => { setTimeout(() => { hideLoading(); location.reload(); }, 500); })";
  html += ".catch(error => { hideLoading(); console.error('Loi:', error); alert('Khong the ket noi den dong co!'); });";
  html += "}";
  html += "function updateSpeed(value) {";
  html += "document.getElementById('speedValue').textContent = value;";
  html += "fetch('/motor/speed?value=' + value)";
  html += ".then(response => response.text())";
  html += ".catch(error => console.error('Loi:', error));";
  html += "}";
  html += "function vibrate() { if (navigator.vibrate) navigator.vibrate(50); }";
  html += "document.addEventListener('DOMContentLoaded', function() {";
  html += "document.querySelectorAll('.btn').forEach(btn => {";
  html += "btn.addEventListener('touchstart', vibrate);";
  html += "});";
  html += "});";
  html += "let autoRefreshEnabled = true;";
  html += "let hapticEnabled = true;";
  html += "let refreshInterval;";
  html += "function startAutoRefresh() {";
  html += "if (autoRefreshEnabled) {";
  html += "refreshInterval = setInterval(() => { location.reload(); }, 30000);";
  html += "}";
  html += "}";
  html += "function toggleMenu() {";
  html += "const sidebar = document.getElementById('sidebar');";
  html += "const overlay = document.querySelector('.overlay');";
  html += "const menuToggle = document.querySelector('.menu-toggle');";
  html += "sidebar.classList.toggle('active');";
  html += "overlay.classList.toggle('active');";
  html += "menuToggle.classList.toggle('active');";
  html += "if (hapticEnabled) vibrate();";
  html += "}";
  html += "function closeMenu() {";
  html += "const sidebar = document.getElementById('sidebar');";
  html += "const overlay = document.querySelector('.overlay');";
  html += "const menuToggle = document.querySelector('.menu-toggle');";
  html += "sidebar.classList.remove('active');";
  html += "overlay.classList.remove('active');";
  html += "menuToggle.classList.remove('active');";
  html += "}";
  html += "function toggleAutoRefresh(checkbox) {";
  html += "autoRefreshEnabled = checkbox.checked;";
  html += "if (autoRefreshEnabled) {";
  html += "startAutoRefresh();";
  html += "} else {";
  html += "clearInterval(refreshInterval);";
  html += "}";
  html += "if (hapticEnabled) vibrate();";
  html += "}";
  html += "function toggleHaptic(checkbox) {";
  html += "hapticEnabled = checkbox.checked;";
  html += "if (hapticEnabled) vibrate();";
  html += "}";
  html += "document.addEventListener('keydown', function(e) {";
  html += "if (e.key === 'Escape') closeMenu();";
  html += "});";
  html += "window.addEventListener('orientationchange', function() {";
  html += "setTimeout(function() {";
  html += "closeMenu();";
  html += "window.scrollTo(0, 0);";
  html += "}, 100);";
  html += "});";
  html += "window.addEventListener('resize', function() {";
  html += "closeMenu();";
  html += "});";
  html += "document.addEventListener('touchstart', function(e) {";
  html += "if (e.touches.length > 1) e.preventDefault();";
  html += "}, { passive: false });";
  html += "let lastTouchEnd = 0;";
  html += "document.addEventListener('touchend', function(e) {";
  html += "const now = (new Date()).getTime();";
  html += "if (now - lastTouchEnd <= 300) e.preventDefault();";
  html += "lastTouchEnd = now;";
  html += "}, false);";
  html += "startAutoRefresh();";
  html += "if ('serviceWorker' in navigator) { navigator.serviceWorker.register('/sw.js').catch(e => console.log('SW registration failed')); }";
  html += "</script></body></html>";
  return html;
}

void setup() {
  // Khởi tạo Serial Monitor
  Serial.begin(115200);
  Serial.println();
  Serial.println("Khởi động ESP8266...");

  // Cấu hình chân LED
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW); // Tắt LED ban đầu (HIGH = bật, LOW = tắt với LED ngoài)

  // Cấu hình chân động cơ DC
  pinMode(MOTOR_PIN1, OUTPUT);
  pinMode(MOTOR_PIN2, OUTPUT);
  pinMode(MOTOR_ENABLE, OUTPUT);
  digitalWrite(MOTOR_PIN1, LOW);
  digitalWrite(MOTOR_PIN2, LOW);
  analogWrite(MOTOR_ENABLE, 0); // Dừng động cơ ban đầu

  // Cấu hình IP tĩnh trước khi kết nối WiFi
  if (useStaticIP) {
    Serial.println("🔧 Cấu hình IP tĩnh...");
    if (!WiFi.config(local_IP, gateway, subnet, primaryDNS, secondaryDNS)) {
      Serial.println("❌ LỖI: Không thể cấu hình IP tĩnh!");
      Serial.println("� Sẽ thử DHCP nếu kết nối thất bại");
      useStaticIP = false;
    } else {
      Serial.println("✅ IP tĩnh đã được cấu hình thành công:");
      Serial.print("🌐 IP mong muốn: ");
      Serial.println(local_IP);
      Serial.print("🚪 Gateway: ");
      Serial.println(gateway);
      Serial.print("🔗 Subnet: ");
      Serial.println(subnet);
    }
  } else {
    Serial.println("🔧 Sử dụng DHCP (IP động)...");
  }

  // Thử kết nối WiFi
  Serial.println("📡 Đang thử kết nối WiFi...");
  Serial.print("🔐 SSID: ");
  Serial.println(ssid);
  WiFi.begin(ssid, password);

  // Chờ kết nối WiFi trong 15 giây (tăng thời gian chờ)
  int attempts = 0;
  Serial.print("⏳ Đang kết nối");
  while (WiFi.status() != WL_CONNECTED && attempts < 30) {
    delay(500);
    Serial.print(".");
    attempts++;

    // Hiển thị trạng thái kết nối
    if (attempts % 10 == 0) {
      Serial.println();
      Serial.print("🔄 Thử lần " + String(attempts/10) + "/3");
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    // Kết nối WiFi thành công
    isWiFiConnected = true;
    Serial.println();
    Serial.println("✅ WiFi đã kết nối thành công!");
    printNetworkInfo();
    Serial.println("🌐 Truy cập: http://" + WiFi.localIP().toString());
  } else {
    // Không thể kết nối WiFi với IP tĩnh, thử DHCP
    Serial.println();
    Serial.println("❌ KHÔNG THỂ KẾT NỐI WIFI VỚI IP TĨNH!");

    // Thử lại với DHCP nếu đang dùng static IP
    if (useStaticIP) {
      Serial.println("🔄 Thử lại với DHCP (IP động)...");
      useStaticIP = false;

      // Reset WiFi và thử DHCP
      WiFi.disconnect();
      delay(1000);
      WiFi.begin(ssid, password);

      // Chờ kết nối DHCP
      int dhcp_attempts = 0;
      Serial.print("⏳ Đang kết nối DHCP");
      while (WiFi.status() != WL_CONNECTED && dhcp_attempts < 20) {
        delay(500);
        Serial.print(".");
        dhcp_attempts++;
      }

      if (WiFi.status() == WL_CONNECTED) {
        isWiFiConnected = true;
        Serial.println();
        Serial.println("✅ Kết nối DHCP thành công!");
        printNetworkInfo();
        Serial.println("💡 Ghi nhớ IP này để cấu hình static IP sau:");
        Serial.println("   IPAddress local_IP(" +
          String(WiFi.localIP()[0]) + ", " +
          String(WiFi.localIP()[1]) + ", " +
          String(WiFi.localIP()[2]) + ", " +
          String(WiFi.localIP()[3]) + ");");
        Serial.println("🌐 Truy cập: http://" + WiFi.localIP().toString());
        return; // Thoát khỏi setup, không cần AP mode
      }
    }

    // Nếu cả static IP và DHCP đều thất bại, chuyển sang AP mode
    Serial.println();
    Serial.println("❌ KHÔNG THỂ KẾT NỐI WIFI!");
    Serial.println("🔧 Nguyên nhân có thể:");
    Serial.println("   - Sai tên WiFi hoặc mật khẩu");
    Serial.println("   - WiFi không khả dụng");
    Serial.println("   - ESP8266 quá xa router");
    Serial.println();
    Serial.println("🔄 Chuyển sang chế độ Access Point...");

    WiFi.mode(WIFI_AP);
    WiFi.softAP(ap_ssid, ap_password);
    isAPMode = true;

    Serial.println("✅ Access Point đã được tạo!");
    Serial.print("📶 SSID: ");
    Serial.println(ap_ssid);
    Serial.print("🔐 Password: ");
    Serial.println(ap_password);
    Serial.print("🌐 IP Address: ");
    Serial.println(WiFi.softAPIP());
    Serial.println("📱 Hướng dẫn:");
    Serial.println("   1. Kết nối điện thoại vào WiFi: " + String(ap_ssid));
    Serial.println("   2. Truy cập: http://" + WiFi.softAPIP().toString());
  }

  // Cấu hình các route cho web server
  server.on("/", handleRoot);
  server.on("/on", handleLEDOn);
  server.on("/off", handleLEDOff);
  server.on("/motor/forward", handleMotorForward);
  server.on("/motor/reverse", handleMotorReverse);
  server.on("/motor/stop", handleMotorStop);
  server.on("/motor/speed", handleMotorSpeed);
  server.on("/status", handleStatus);
  server.on("/manifest.json", handleManifest);
  server.on("/sw.js", handleServiceWorker);

  // Khởi động web server
  server.begin();
  Serial.println("Web server đã khởi động!");
}

void loop() {
  // Xử lý các request từ web server
  server.handleClient();

  // Kiểm tra kết nối WiFi định kỳ (mỗi 30 giây)
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 30000) {
    lastCheck = millis();
    checkWiFiConnection();
  }
}

// Hàm kiểm tra và quản lý kết nối WiFi
void checkWiFiConnection() {
  if (isWiFiConnected && WiFi.status() != WL_CONNECTED) {
    // Mất kết nối WiFi, chuyển sang AP mode
    Serial.println("Mất kết nối WiFi! Chuyển sang chế độ Access Point...");
    isWiFiConnected = false;
    isAPMode = true;

    WiFi.mode(WIFI_AP);
    WiFi.softAP(ap_ssid, ap_password);

    Serial.println("Access Point đã được kích hoạt!");
    Serial.println("Kết nối vào WiFi: " + String(ap_ssid));
    Serial.println("Truy cập: http://" + WiFi.softAPIP().toString());
  }
  else if (isAPMode && !isWiFiConnected) {
    // Đang ở chế độ AP, thử kết nối lại WiFi
    Serial.println("Thử kết nối lại WiFi...");
    WiFi.mode(WIFI_STA);
    WiFi.begin(ssid, password);

    // Chờ 5 giây để kết nối
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 10) {
      delay(500);
      attempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
      isWiFiConnected = true;
      isAPMode = false;
      Serial.println("Đã kết nối lại WiFi thành công!");
      Serial.println("IP Address: " + WiFi.localIP().toString());
    } else {
      // Vẫn không kết nối được, quay lại AP mode
      WiFi.mode(WIFI_AP);
      WiFi.softAP(ap_ssid, ap_password);
      Serial.println("Không thể kết nối WiFi, tiếp tục chế độ AP");
    }
  }
}

// Xử lý trang chủ
void handleRoot() {
  String html = getHTMLPage();
  server.send(200, "text/html", html);
}

// Xử lý bật LED
void handleLEDOn() {
  ledState = true;
  digitalWrite(LED_PIN, HIGH); // HIGH = bật LED
  Serial.println("LED đã được BẬT");
  server.send(200, "text/plain", "LED ON");
}

// Xử lý tắt LED
void handleLEDOff() {
  ledState = false;
  digitalWrite(LED_PIN, LOW); // LOW = tắt LED
  Serial.println("LED đã được TẮT");
  server.send(200, "text/plain", "LED OFF");
}

// Xử lý trạng thái LED (API endpoint)
void handleStatus() {
  String status = "{\"led\":\"" + String(ledState ? "on" : "off") + "\",\"uptime\":" + String(millis() / 1000) + "}";
  server.send(200, "application/json", status);
}

// Xử lý manifest.json cho PWA
void handleManifest() {
  String manifest = "{";
  manifest += "\"name\":\"Smart Home Control\",";
  manifest += "\"short_name\":\"SmartHome\",";
  manifest += "\"description\":\"ESP8266 Smart Home Control App\",";
  manifest += "\"start_url\":\"/\",";
  manifest += "\"display\":\"standalone\",";
  manifest += "\"background_color\":\"#667eea\",";
  manifest += "\"theme_color\":\"#2196F3\",";
  manifest += "\"orientation\":\"portrait\",";
  manifest += "\"icons\":[";
  manifest += "{\"src\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==\",\"sizes\":\"192x192\",\"type\":\"image/png\"}";
  manifest += "]}";
  server.send(200, "application/json", manifest);
}

// Xử lý service worker cho PWA
void handleServiceWorker() {
  String sw = "const CACHE_NAME = 'smart-home-v1';";
  sw += "const urlsToCache = ['/'];";
  sw += "self.addEventListener('install', event => {";
  sw += "event.waitUntil(caches.open(CACHE_NAME).then(cache => cache.addAll(urlsToCache)));";
  sw += "});";
  sw += "self.addEventListener('fetch', event => {";
  sw += "event.respondWith(caches.match(event.request).then(response => response || fetch(event.request)));";
  sw += "});";
  server.send(200, "application/javascript", sw);
}

// Xử lý điều khiển động cơ quay thuận
void handleMotorForward() {
  motorState = 1;
  digitalWrite(MOTOR_PIN1, HIGH);
  digitalWrite(MOTOR_PIN2, LOW);
  analogWrite(MOTOR_ENABLE, motorSpeed);
  Serial.println("Động cơ quay THUẬN với tốc độ: " + String(motorSpeed));
  server.send(200, "text/plain", "MOTOR FORWARD");
}

// Xử lý điều khiển động cơ quay nghịch
void handleMotorReverse() {
  motorState = -1;
  digitalWrite(MOTOR_PIN1, LOW);
  digitalWrite(MOTOR_PIN2, HIGH);
  analogWrite(MOTOR_ENABLE, motorSpeed);
  Serial.println("Động cơ quay NGHỊCH với tốc độ: " + String(motorSpeed));
  server.send(200, "text/plain", "MOTOR REVERSE");
}

// Xử lý dừng động cơ
void handleMotorStop() {
  motorState = 0;
  digitalWrite(MOTOR_PIN1, LOW);
  digitalWrite(MOTOR_PIN2, LOW);
  analogWrite(MOTOR_ENABLE, 0);
  Serial.println("Động cơ đã DỪNG");
  server.send(200, "text/plain", "MOTOR STOP");
}

// Xử lý thay đổi tốc độ động cơ
void handleMotorSpeed() {
  if (server.hasArg("value")) {
    int newSpeed = server.arg("value").toInt();
    if (newSpeed >= 0 && newSpeed <= 255) {
      motorSpeed = newSpeed;
      // Cập nhật tốc độ nếu động cơ đang chạy
      if (motorState != 0) {
        analogWrite(MOTOR_ENABLE, motorSpeed);
      }
      Serial.println("Tốc độ động cơ đã thay đổi: " + String(motorSpeed));
      server.send(200, "text/plain", "SPEED UPDATED");
    } else {
      server.send(400, "text/plain", "INVALID SPEED VALUE");
    }
  } else {
    server.send(400, "text/plain", "MISSING SPEED VALUE");
  }
}

// Hàm hiển thị thông tin mạng chi tiết
void printNetworkInfo() {
  Serial.println("📊 THÔNG TIN MẠNG:");
  Serial.println("==================");
  Serial.print("🌐 IP Address: ");
  Serial.println(WiFi.localIP());
  Serial.print("🚪 Gateway: ");
  Serial.println(WiFi.gatewayIP());
  Serial.print("🔗 Subnet Mask: ");
  Serial.println(WiFi.subnetMask());
  Serial.print("📡 DNS Server: ");
  Serial.println(WiFi.dnsIP());
  Serial.print("📶 RSSI: ");
  Serial.print(WiFi.RSSI());
  Serial.println(" dBm");
  Serial.print("🔐 SSID: ");
  Serial.println(WiFi.SSID());
  Serial.print("📍 MAC Address: ");
  Serial.println(WiFi.macAddress());
  Serial.println("==================");
}