# Hệ thống điều khiển đèn ESP8266 qua Web

Dự án này cho phép bạn điều khiển đèn LED bằng ESP8266 thông qua giao diện web đơn giản và thân thiện.

## Tính năng

- ✅ Điều khiển bật/tắt đèn LED qua web browser
- ✅ Giao diện web responsive, thân thiện với mobile
- ✅ Hiển thị trạng thái đèn real-time
- ✅ Hiển thị IP address và thời gian hoạt động
- ✅ Tự động refresh trang mỗi 30 giây
- ✅ API endpoint để kiểm tra trạng thái

## Cấu hình cần thiết

### 1. Cấu hình WiFi

Mở file `src/main.cpp` và thay đổi thông tin WiFi của bạn:

```cpp
const char* ssid = "YOUR_WIFI_NAME";     // Thay bằng tên Wi<PERSON>i của bạn
const char* password = "YOUR_WIFI_PASSWORD"; // Thay bằng mật khẩu WiFi
```

### 2. C<PERSON>u hình chân LED (tùy chọn)

Mặc định sử dụng GPIO2 (D4 trên NodeMCU). Bạn có thể thay đổi:

```cpp
const int LED_PIN = 2;  // Thay đổi số chân nếu cần
```

## Cách sử dụng

### 1. Upload code lên ESP8266

```bash
# Trong thư mục dự án
pio run --target upload
```

### 2. Mở Serial Monitor để xem thông tin

```bash
pio device monitor
```

### 3. Truy cập giao diện web

Sau khi ESP8266 kết nối WiFi thành công, bạn sẽ thấy IP address trong Serial Monitor.
Mở trình duyệt và truy cập địa chỉ đó, ví dụ: `http://*************`

## Giao diện Web

Giao diện web bao gồm:

- **Tiêu đề**: "Dieu khien den ESP8266"
- **Trạng thái đèn**: Hiển thị "BAT" hoặc "TAT" với màu sắc tương ứng
- **Nút điều khiển**: 
  - Nút xanh "BAT DEN" để bật đèn
  - Nút đỏ "TAT DEN" để tắt đèn
- **Thông tin hệ thống**: IP address và thời gian hoạt động

## API Endpoints

- `GET /` - Trang chủ với giao diện điều khiển
- `GET /on` - Bật đèn LED
- `GET /off` - Tắt đèn LED  
- `GET /status` - Lấy trạng thái đèn dạng JSON

Ví dụ response của `/status`:
```json
{
  "led": "on",
  "uptime": 1234
}
```

## Sơ đồ kết nối

```
ESP8266 (NodeMCU)    LED
GPIO2 (D4) ---------> Anode (+)
GND --------------------> Cathode (-)
```

**Lưu ý**: Với LED built-in trên ESP8266:
- `digitalWrite(LED_PIN, LOW)` = Bật đèn
- `digitalWrite(LED_PIN, HIGH)` = Tắt đèn

## Troubleshooting

### ESP8266 không kết nối được WiFi
- Kiểm tra tên WiFi và mật khẩu
- Đảm bảo WiFi là 2.4GHz (ESP8266 không hỗ trợ 5GHz)
- Kiểm tra tín hiệu WiFi đủ mạnh

### Không truy cập được web
- Kiểm tra IP address trong Serial Monitor
- Đảm bảo máy tính và ESP8266 cùng mạng WiFi
- Thử tắt firewall tạm thời

### LED không hoạt động
- Kiểm tra chân kết nối
- Thử thay đổi `LED_PIN` sang chân khác
- Kiểm tra LED có bị hỏng không

## Mở rộng

Bạn có thể mở rộng dự án này bằng cách:

- Thêm nhiều LED hoặc thiết bị khác
- Thêm cảm biến (nhiệt độ, độ ẩm, ánh sáng)
- Tích hợp với Home Assistant hoặc các hệ thống smart home khác
- Thêm authentication để bảo mật
- Lưu trạng thái vào EEPROM để nhớ sau khi reset

## Yêu cầu phần cứng

- ESP8266 (NodeMCU, Wemos D1 Mini, hoặc tương tự)
- LED (tùy chọn, có thể dùng LED built-in)
- Điện trở 220Ω (nếu dùng LED ngoài)
- Breadboard và dây nối (nếu cần)

## Thư viện sử dụng

- `ESP8266WiFi` - Kết nối WiFi
- `ESP8266WebServer` - Tạo web server

Các thư viện này đã có sẵn trong ESP8266 Arduino Core.
