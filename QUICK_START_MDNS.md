# 🚀 Hướng dẫn nhanh - ESP8266 với mDNS

## 📋 Tóm tắt
Chương trình ESP8266 đơn giản với mDNS cho phép truy cập qua `http://esp.local` thay vì phải nhớ địa chỉ IP.

## ⚡ Bắt đầu nhanh

### 1. <PERSON><PERSON><PERSON><PERSON> sang phiên bản mDNS
```bash
# Sử dụng script tự động
./switch.sh simple

# Hoặc thủ công
mv src/main.cpp src/main_full.cpp
mv src/simple_mdns.cpp src/main.cpp
```

### 2. Cấu hình WiFi
Mở `src/main.cpp` và thay đổi:
```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";        // ← Thay đổi này
const char* password = "MAT_KHAU_WIFI";       // ← Thay đổi này
const char* mdns_name = "esp";                // ← <PERSON><PERSON> thể thay đổi
```

### 3. Upload và chạy
```bash
pio run --target upload
pio device monitor
```

### 4. T<PERSON>y cập
- **mDNS**: `http://esp.local` 🎯
- **IP**: `http://192.168.1.xxx` (xem Serial Monitor)

## 🔧 Cấu hình nâng cao

### Thay đổi tên miền
```cpp
const char* mdns_name = "smarthome";  // → http://smarthome.local
const char* mdns_name = "iot";        // → http://iot.local
const char* mdns_name = "esp32";      // → http://esp32.local
```

### Kiểm tra mDNS hoạt động
```bash
# Ping để test
ping esp.local

# Nếu không hoạt động, sử dụng IP trực tiếp
ping *************  # IP từ Serial Monitor
```

## 📱 Hỗ trợ mDNS trên các thiết bị

| Thiết bị | Hỗ trợ | Cách bật |
|----------|--------|----------|
| **Windows** | ⚠️ Cần cài đặt | Cài iTunes hoặc Bonjour Print Services |
| **macOS** | ✅ Sẵn có | Không cần làm gì |
| **Linux** | ⚠️ Cần cài đặt | `sudo apt install avahi-daemon` |
| **Android** | ⚠️ App | Cài "Bonjour Browser" |
| **iOS** | ✅ Sẵn có | Không cần làm gì |

## 🔍 Troubleshooting

### ❌ Không truy cập được esp.local

**Nguyên nhân**: Thiết bị không hỗ trợ mDNS

**Giải pháp**:
1. Sử dụng IP trực tiếp (xem Serial Monitor)
2. Cài đặt Bonjour cho Windows
3. Kiểm tra cùng mạng WiFi

### ❌ ESP8266 không kết nối WiFi

**Nguyên nhân**: Sai thông tin WiFi hoặc tín hiệu yếu

**Giải pháp**:
1. Kiểm tra SSID và password
2. Đảm bảo WiFi 2.4GHz (không phải 5GHz)
3. Đưa ESP8266 gần router hơn

### ❌ Serial Monitor không hiển thị gì

**Nguyên nhân**: Sai baud rate hoặc port

**Giải pháp**:
```bash
# Kiểm tra port
pio device list

# Sử dụng baud rate đúng (115200)
pio device monitor --baud 115200
```

## 📊 So sánh với phiên bản đầy đủ

| Tính năng | mDNS Simple | Full Version |
|-----------|-------------|--------------|
| Truy cập qua tên miền | ✅ esp.local | ❌ Chỉ IP |
| Điều khiển LED | ❌ | ✅ |
| Điều khiển Motor | ❌ | ✅ |
| Giao diện đẹp | ❌ | ✅ |
| Kích thước code | 📦 Nhỏ | 📦📦📦 Lớn |
| Độ phức tạp | 🟢 Đơn giản | 🔴 Phức tạp |

## 🔄 Chuyển đổi phiên bản

### Về phiên bản đầy đủ:
```bash
./switch.sh full
```

### Kiểm tra phiên bản hiện tại:
```bash
./switch.sh status
```

## 📝 Thông tin hiển thị trên web

Trang web sẽ hiển thị:
- 🌟 Lời chào mừng
- 📡 Thông tin WiFi (SSID, IP, Gateway, RSSI)
- ⚡ Thông tin hệ thống (Uptime, Memory, CPU)
- 🔗 Cách truy cập (IP và mDNS)

## 🎯 Ứng dụng thực tế

### Phù hợp cho:
- ✅ Học tập về mDNS
- ✅ Web server đơn giản
- ✅ Hiển thị thông tin thiết bị
- ✅ Base code cho dự án lớn hơn

### Không phù hợp cho:
- ❌ Điều khiển thiết bị phức tạp
- ❌ Giao diện đẹp cho người dùng cuối
- ❌ Ứng dụng mobile chuyên nghiệp

## 📚 Tài liệu thêm

- [README_MDNS_SIMPLE.md](README_MDNS_SIMPLE.md) - Hướng dẫn chi tiết
- [SWITCH_VERSION.md](SWITCH_VERSION.md) - Cách chuyển đổi phiên bản
- [ESP8266 mDNS Documentation](https://arduino-esp8266.readthedocs.io/en/latest/esp8266wifi/readme.html#mdns-and-dns-sd)

---

💡 **Tip**: Nếu bạn cần cả mDNS và điều khiển thiết bị, có thể kết hợp code từ cả hai phiên bản!
