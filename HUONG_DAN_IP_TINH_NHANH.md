# ⚡ HƯỚNG DẪN NHANH - CẤU HÌNH IP TĨNH

## 🎯 Mục tiêu:
**<PERSON>ố định IP cho ESP8266** để luôn truy cập cùng một địa chỉ

## 🔍 BƯỚC 1: Tì<PERSON> thông tin mạng

### 📱 **<PERSON><PERSON><PERSON> nhanh nhất:**
1. **Mở Command Prompt** (Windows) hoặc **Terminal** (Mac/Linux)
2. **<PERSON><PERSON> lệnh**: `ipconfig` (Windows) hoặc `ifconfig` (Mac/Linux)
3. **Tìm thông tin**:
   ```
   Default Gateway: ***********    ← IP Router
   Subnet Mask: *************      ← Subnet
   IPv4 Address: 192.168.1.xxx     ← Dải IP
   ```

### 📋 **V<PERSON> dụ kết quả:**
```
Wireless LAN adapter Wi-Fi:
   IPv4 Address: ***********05
   Subnet Mask: *************
   Default Gateway: ***********
```

## ⚙️ BƯỚC 2: C<PERSON>p nhật code

### 🔧 **Thay đổi trong file main.cpp:**
```cpp
// Tìm dòng này (khoảng dòng 8-12):
IPAddress local_IP(192, 168, 1, 100);      // ← Thay đổi IP này
IPAddress gateway(192, 168, 1, 1);         // ← Thay đổi Gateway
IPAddress subnet(255, 255, 255, 0);        // ← Thay đổi Subnet
```

### 💡 **Cách chọn IP:**
- **Nếu Gateway là ************* → Chọn IP: `*************`
- **Nếu Gateway là ************* → Chọn IP: `***********00`  
- **Nếu Gateway là ********** → Chọn IP: `********00`

## 📝 BƯỚC 3: Cấu hình theo mạng phổ biến

### 🏠 **Mạng 192.168.1.x (Phổ biến nhất):**
```cpp
IPAddress local_IP(192, 168, 1, 100);
IPAddress gateway(192, 168, 1, 1);
IPAddress subnet(255, 255, 255, 0);
```

### 🏠 **Mạng 192.168.0.x:**
```cpp
IPAddress local_IP(192, 168, 0, 100);
IPAddress gateway(192, 168, 0, 1);
IPAddress subnet(255, 255, 255, 0);
```

### 🏠 **Mạng 10.0.0.x:**
```cpp
IPAddress local_IP(10, 0, 0, 100);
IPAddress gateway(10, 0, 0, 1);
IPAddress subnet(255, 255, 255, 0);
```

## 🚀 BƯỚC 4: Upload và test

### 📤 **Upload code:**
1. **Lưu file** main.cpp
2. **Upload** lên ESP8266
3. **Mở Serial Monitor** (115200 baud)

### 📊 **Kiểm tra log:**
```
📊 THÔNG TIN MẠNG:
==================
🌐 IP Address: *************
🚪 Gateway: ***********
🔗 Subnet Mask: *************
📡 DNS Server: *******
📶 RSSI: -45 dBm
🔐 SSID: Dau Lac
📍 MAC Address: XX:XX:XX:XX:XX:XX
==================
🌐 Truy cập: http://*************
```

### 🌐 **Truy cập:**
- **Mở trình duyệt**
- **Gõ địa chỉ**: `http://*************`
- **Bookmark** để dễ truy cập sau này

## ⚠️ Troubleshooting

### ❌ **Không kết nối được WiFi:**
```cpp
// Thử IP khác:
IPAddress local_IP(192, 168, 1, 101);  // Thay 100 → 101
```

### ❌ **Kết nối WiFi nhưng không truy cập được:**
1. **Ping test**: `ping *************`
2. **Thử từ điện thoại** (cùng WiFi)
3. **Kiểm tra firewall**

### ❌ **IP bị xung đột:**
```cpp
// Chọn IP khác:
IPAddress local_IP(192, 168, 1, 150);  // Thử IP khác
```

## 🎯 **Template nhanh:**

### 📋 **Copy-paste và thay đổi:**
```cpp
// THAY ĐỔI CÁC SỐ THEO MẠNG CỦA BẠN:
IPAddress local_IP(192, 168, 1, 100);      // IP bạn muốn
IPAddress gateway(192, 168, 1, 1);         // IP router
IPAddress subnet(255, 255, 255, 0);        // Subnet mask
IPAddress primaryDNS(8, 8, 8, 8);          // Google DNS
IPAddress secondaryDNS(8, 8, 4, 4);        // Google DNS
```

## ✅ **Kết quả:**

### 🎉 **Trước (IP động):**
```
Lần 1: http://***********56
Lần 2: http://*************  
Lần 3: http://***********78
```

### 🎯 **Sau (IP tĩnh):**
```
Mãi mãi: http://*************
```

## 💡 **Mẹo hay:**

### 📱 **Bookmark trên điện thoại:**
1. Truy cập `http://*************`
2. **Add to Home Screen** (iOS) hoặc **Add to Desktop** (Android)
3. Có icon như app thật!

### 🔖 **Ghi nhớ IP:**
- **Dễ nhớ**: Chọn IP đẹp như 100, 111, 123
- **Ghi chú**: Lưu IP vào notes
- **QR Code**: Tạo QR code cho IP

Bây giờ ESP8266 sẽ luôn có IP cố định! 🎉
