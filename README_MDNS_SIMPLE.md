# ESP8266 mDNS Web Server - Phiên bản đơn giản

## 📋 Mô tả
Chương trình ESP8266 đơn giản với mDNS cho phép truy cập thiết bị qua tên miền `http://esp.local` thay vì phải nhớ địa chỉ IP.

## ✨ Tính năng
- ✅ Kết nối WiFi tự động
- ✅ mDNS để truy cập qua `http://esp.local`
- ✅ Web server hiển thị thông tin hệ thống
- ✅ Giao diện web đơn giản, thân thiện
- ✅ Tự động kiểm tra và kết nối lại WiFi
- ✅ Hiển thị thông tin chi tiết trên Serial Monitor

## 🔧 Cấu hình

### 1. Thay đổi thông tin WiFi
Mở file `src/simple_mdns.cpp` và chỉnh sửa:

```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";        // Thay đổi tên WiFi
const char* password = "MAT_KHAU_WIFI";       // Thay đổi mật khẩu WiFi
const char* mdns_name = "esp";                // Tên miền (esp.local)
```

### 2. Thay đổi tên miền (tùy chọn)
Nếu muốn sử dụng tên khác thay vì `esp.local`:

```cpp
const char* mdns_name = "smarthome";  // Truy cập qua http://smarthome.local
```

## 🚀 Cách sử dụng

### 1. Upload chương trình
```bash
# Biên dịch và upload
pio run --target upload

# Hoặc chỉ biên dịch
pio run
```

### 2. Theo dõi Serial Monitor
```bash
pio device monitor
```

### 3. Truy cập web server
Sau khi ESP8266 kết nối WiFi thành công, bạn có thể truy cập bằng:

- **mDNS**: `http://esp.local` (khuyến nghị)
- **IP trực tiếp**: `http://192.168.1.xxx` (IP sẽ hiển thị trên Serial Monitor)

## 📱 Giao diện Web
Trang web hiển thị:
- 🌟 Lời chào mừng
- 📡 Thông tin kết nối WiFi (SSID, IP, Gateway, cường độ tín hiệu)
- ⚡ Thông tin hệ thống (uptime, bộ nhớ, tần số CPU)

## 🔍 Troubleshooting

### Không truy cập được qua esp.local?

1. **Kiểm tra mDNS support**:
   - **Windows**: Cài đặt iTunes hoặc Bonjour Print Services
   - **macOS**: Hỗ trợ sẵn
   - **Linux**: Cài đặt `avahi-daemon`
   - **Android**: Sử dụng app "Bonjour Browser"
   - **iOS**: Hỗ trợ sẵn

2. **Thử các cách khác**:
   ```bash
   # Ping để kiểm tra
   ping esp.local
   
   # Sử dụng IP trực tiếp (xem trên Serial Monitor)
   http://***********00
   ```

3. **Kiểm tra cùng mạng WiFi**:
   - Đảm bảo máy tính/điện thoại và ESP8266 cùng mạng WiFi
   - Một số router có thể chặn mDNS giữa các thiết bị

### ESP8266 không kết nối WiFi?

1. **Kiểm tra thông tin WiFi**:
   - SSID và password đúng chưa?
   - WiFi có hỗ trợ 2.4GHz không? (ESP8266 không hỗ trợ 5GHz)

2. **Kiểm tra khoảng cách**:
   - ESP8266 có gần router không?
   - Có vật cản nào không?

3. **Kiểm tra Serial Monitor**:
   - Xem thông báo lỗi chi tiết
   - Kiểm tra trạng thái kết nối

## 📝 Thông tin Serial Monitor

Khi chạy thành công, Serial Monitor sẽ hiển thị:

```
========================================
🚀 ESP8266 mDNS Web Server Starting...
========================================
📡 Đang kết nối WiFi: TEN_WIFI
.....
✅ WiFi đã kết nối thành công!
========================================
📊 THÔNG TIN MẠNG:
🌐 Địa chỉ IP: ***********00
🚪 Gateway: ***********
📶 Cường độ tín hiệu: -45 dBm
========================================
🔧 Đang cấu hình mDNS: esp.local
✅ mDNS đã khởi tạo thành công!
🌐 Truy cập qua: http://esp.local
📡 HTTP service đã được đăng ký
🌐 Web server đã khởi động!
========================================
🎉 SẴN SÀNG SỬ DỤNG!
Truy cập bằng một trong các cách sau:
   • http://***********00
   • http://esp.local
========================================
```

## 🔄 So sánh với phiên bản đầy đủ

| Tính năng | Simple mDNS | Full Version |
|-----------|-------------|--------------|
| mDNS | ✅ | ❌ |
| Điều khiển LED | ❌ | ✅ |
| Điều khiển động cơ | ❌ | ✅ |
| Giao diện phức tạp | ❌ | ✅ |
| PWA Support | ❌ | ✅ |
| AP Mode fallback | ❌ | ✅ |
| Responsive design | ❌ | ✅ |

## 📚 Tài liệu tham khảo
- [ESP8266 mDNS Library](https://arduino-esp8266.readthedocs.io/en/latest/esp8266wifi/readme.html#mdns-and-dns-sd)
- [mDNS Wikipedia](https://en.wikipedia.org/wiki/Multicast_DNS)
- [Bonjour/mDNS on different platforms](https://developer.apple.com/bonjour/)
