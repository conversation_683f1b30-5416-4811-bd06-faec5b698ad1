# Hướng dẫn cài đặt và sử dụng

## Bước 1: <PERSON><PERSON><PERSON> đặt PlatformIO

### Cách 1: Sử dụng VS Code Extension (Khuyến nghị)

1. Mở VS Code
2. Vào Extensions (Ctrl+Shift+X)
3. <PERSON><PERSON><PERSON> "PlatformIO IDE"
4. Cài đặt extension
5. Restart VS Code

### Cách 2: Cài đặt PlatformIO CLI

```bash
# Cài đặt Python pip nếu chưa có
# macOS
brew install python

# Cài đặt PlatformIO
pip install platformio

# Hoặc sử dụng pip3
pip3 install platformio
```

## Bước 2: Chuẩn bị phần cứng

### ESP8266 boards được hỗ trợ:
- NodeMCU v1.0 (ESP-12E Module)
- Wemos D1 Mini
- ESP8266 Generic
- Adafruit Feather HUZZAH ESP8266

### Sơ đồ chân cho NodeMCU:
```
NodeMCU Pin | GPIO | Chức năng
------------|------|----------
D0          | 16   | Wake up
D1          | 5    | I2C SCL
D2          | 4    | I2C SDA
D3          | 0    | Flash button
D4          | 2    | LED built-in (mặc định)
D5          | 14   | SPI CLK
D6          | 12   | SPI MISO
D7          | 13   | SPI MOSI
D8          | 15   | SPI CS
```

## Bước 3: Cấu hình dự án

### 3.1 Mở dự án trong VS Code
1. Mở VS Code
2. File → Open Folder
3. Chọn thư mục dự án `esp_2`

### 3.2 Cấu hình WiFi
Mở file `src/main.cpp` và sửa:

```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";
const char* password = "MAT_KHAU_WIFI";
```

**Lưu ý quan trọng:**
- WiFi phải là 2.4GHz (ESP8266 không hỗ trợ 5GHz)
- Tên WiFi và mật khẩu phân biệt hoa thường
- Không sử dụng ký tự đặc biệt trong tên WiFi

### 3.3 Cấu hình chân LED (tùy chọn)

Nếu muốn sử dụng LED ngoài thay vì LED built-in:

```cpp
const int LED_PIN = 5;  // Ví dụ: sử dụng D1 (GPIO5)
```

Và thay đổi logic điều khiển:
```cpp
// Trong handleLEDOn()
digitalWrite(LED_PIN, HIGH); // HIGH = bật LED ngoài

// Trong handleLEDOff()  
digitalWrite(LED_PIN, LOW);  // LOW = tắt LED ngoài
```

## Bước 4: Build và Upload

### Sử dụng VS Code với PlatformIO Extension:

1. Mở Command Palette (Ctrl+Shift+P)
2. Chọn "PlatformIO: Build" để build
3. Kết nối ESP8266 vào máy tính qua USB
4. Chọn "PlatformIO: Upload" để upload code

### Sử dụng Terminal:

```bash
# Build dự án
pio run

# Upload lên ESP8266
pio run --target upload

# Mở Serial Monitor
pio device monitor
```

## Bước 5: Kiểm tra kết quả

### 5.1 Kiểm tra Serial Monitor
Sau khi upload, mở Serial Monitor để xem:
- ESP8266 khởi động
- Kết nối WiFi
- IP address được cấp phát

### 5.2 Truy cập giao diện web
1. Lấy IP address từ Serial Monitor
2. Mở trình duyệt web
3. Truy cập `http://[IP_ADDRESS]`
4. Thử bấm nút "BAT DEN" và "TAT DEN"

## Troubleshooting

### Lỗi upload
```
Error: Failed to connect to ESP8266
```
**Giải pháp:**
- Kiểm tra cáp USB
- Nhấn giữ nút FLASH trên ESP8266 khi upload
- Thử port COM khác
- Kiểm tra driver CH340/CP2102

### Lỗi WiFi
```
WiFi connection failed
```
**Giải pháp:**
- Kiểm tra tên WiFi và mật khẩu
- Đảm bảo WiFi là 2.4GHz
- Kiểm tra tín hiệu WiFi
- Thử reset ESP8266

### Không truy cập được web
**Giải pháp:**
- Kiểm tra IP address trong Serial Monitor
- Ping IP address từ máy tính
- Tắt firewall tạm thời
- Đảm bảo cùng mạng WiFi

### LED không hoạt động
**Giải pháp:**
- Kiểm tra chân kết nối
- Thử LED khác
- Kiểm tra điện trở (220Ω cho LED 3.3V)
- Đo điện áp trên chân GPIO

## Mở rộng dự án

### Thêm nhiều LED:
```cpp
const int LED1_PIN = 2;
const int LED2_PIN = 4;
const int LED3_PIN = 5;

// Thêm các route mới
server.on("/led1/on", handleLED1On);
server.on("/led1/off", handleLED1Off);
```

### Thêm cảm biến DHT22:
```cpp
#include <DHT.h>
DHT dht(DHT_PIN, DHT22);

// Trong getHTMLPage()
html += "<p>Nhiet do: " + String(dht.readTemperature()) + "°C</p>";
html += "<p>Do am: " + String(dht.readHumidity()) + "%</p>";
```

### Thêm MQTT:
```cpp
#include <PubSubClient.h>
WiFiClient espClient;
PubSubClient client(espClient);
```

## Tài liệu tham khảo

- [ESP8266 Arduino Core](https://github.com/esp8266/Arduino)
- [PlatformIO Documentation](https://docs.platformio.org/)
- [ESP8266 Pinout](https://randomnerdtutorials.com/esp8266-pinout-reference-gpios/)
- [ESP8266WebServer Library](https://github.com/esp8266/Arduino/tree/master/libraries/ESP8266WebServer)
