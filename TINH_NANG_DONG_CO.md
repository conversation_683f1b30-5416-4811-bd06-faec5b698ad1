# 🚗 TÍNH NĂNG ĐIỀU KHIỂN ĐỘNG CƠ DC

## ✅ Những gì đã được thêm:

### 🎛️ **Giao diện điều khiển mới:**
- Card điều khiển động cơ DC riêng biệt
- 3 nút điều khiển: THUẬN, NGHỊCH, DỪNG
- Thanh trượt điều chỉnh tốc độ (0-255)
- <PERSON><PERSON><PERSON> thị trạng thái động cơ real-time

### ⚙️ **Chức năng điều khiển:**
- **Quay thuận**: Động cơ quay theo chiều kim đồng hồ
- **Quay nghịch**: Động cơ quay ngược chiều kim đồng hồ  
- **Dừng**: Dừng động cơ ngay lập tức
- **Điều chỉnh tốc độ**: Từ 0 (dừng) đến 255 (tối đa)

### 🔌 **C<PERSON><PERSON> hình phần cứng:**
```cpp
const int MOTOR_PIN1 = 4;    // GPIO4 (D2) - <PERSON><PERSON><PERSON><PERSON> khiển hướng 1
const int MOTOR_PIN2 = 5;    // GPIO5 (D1) - <PERSON><PERSON><PERSON>u khiển hướng 2  
const int MOTOR_ENABLE = 14; // GPIO14 (D5) - PWM điều khiển tốc độ
```

## 🌐 **API Endpoints mới:**

### Điều khiển hướng:
- `GET /motor/forward` - Quay thuận
- `GET /motor/reverse` - Quay nghịch
- `GET /motor/stop` - Dừng động cơ

### Điều khiển tốc độ:
- `GET /motor/speed?value=150` - Đặt tốc độ (0-255)

## 📱 **Giao diện người dùng:**

### Card động cơ hiển thị:
- **Tên thiết bị**: "Dong Co DC" với icon động cơ
- **Trạng thái**: DUNG / THUAN / NGHICH với màu sắc
- **Nút điều khiển**: 3 nút với màu khác nhau
- **Thanh tốc độ**: Slider với giá trị hiện tại

### JavaScript tương tác:
```javascript
// Điều khiển hướng
controlMotor('forward')  // Quay thuận
controlMotor('reverse')  // Quay nghịch  
controlMotor('stop')     // Dừng

// Điều chỉnh tốc độ
updateSpeed(200)         // Đặt tốc độ 200
```

## 🔧 **Logic điều khiển:**

### Quay thuận (motorState = 1):
```cpp
digitalWrite(MOTOR_PIN1, HIGH);  // IN1 = HIGH
digitalWrite(MOTOR_PIN2, LOW);   // IN2 = LOW
analogWrite(MOTOR_ENABLE, motorSpeed); // PWM
```

### Quay nghịch (motorState = -1):
```cpp
digitalWrite(MOTOR_PIN1, LOW);   // IN1 = LOW
digitalWrite(MOTOR_PIN2, HIGH);  // IN2 = HIGH
analogWrite(MOTOR_ENABLE, motorSpeed); // PWM
```

### Dừng (motorState = 0):
```cpp
digitalWrite(MOTOR_PIN1, LOW);   // IN1 = LOW
digitalWrite(MOTOR_PIN2, LOW);   // IN2 = LOW
analogWrite(MOTOR_ENABLE, 0);    // PWM = 0
```

## 🎯 **Tính năng thông minh:**

### 1. **Điều chỉnh tốc độ real-time:**
- Thay đổi tốc độ ngay khi kéo thanh trượt
- Không cần reload trang
- Cập nhật PWM ngay lập tức nếu động cơ đang chạy

### 2. **Trạng thái hiển thị:**
- Màu xanh: Động cơ đang chạy (thuận/nghịch)
- Màu đỏ: Động cơ dừng
- Text hiển thị: DUNG / THUAN / NGHICH

### 3. **Bảo vệ và kiểm tra:**
- Kiểm tra giá trị tốc độ hợp lệ (0-255)
- Dừng an toàn khi chuyển hướng
- Log chi tiết trong Serial Monitor

## 🚀 **Cách sử dụng:**

### Bước 1: Kết nối phần cứng
- Theo hướng dẫn trong `HUONG_DAN_KET_NOI_DONG_CO.md`
- Sử dụng driver L298N
- Nguồn điện riêng cho động cơ

### Bước 2: Upload code
- Upload code đã cập nhật lên ESP8266
- Mở Serial Monitor để xem log

### Bước 3: Truy cập giao diện
- Kết nối WiFi hoặc AP mode
- Mở trình duyệt: `http://[IP_ADDRESS]`
- Sử dụng card "Dong Co DC"

### Bước 4: Điều khiển
1. **Chọn hướng**: Nhấn THUẬN hoặc NGHỊCH
2. **Điều chỉnh tốc độ**: Kéo thanh trượt
3. **Dừng**: Nhấn nút DỪNG khi cần

## 🔍 **Monitoring:**

### Serial Monitor hiển thị:
```
Động cơ quay THUẬN với tốc độ: 200
Tốc độ động cơ đã thay đổi: 150  
Động cơ quay NGHỊCH với tốc độ: 150
Động cơ đã DỪNG
```

### Giao diện web hiển thị:
- Trạng thái real-time
- Tốc độ hiện tại
- Phản hồi ngay lập tức

## 💡 **Ứng dụng thực tế:**

1. **Quạt điều hòa**: Điều khiển hướng và tốc độ quạt
2. **Cửa tự động**: Mở/đóng cửa với tốc độ khác nhau
3. **Robot di chuyển**: Điều khiển bánh xe
4. **Máy trộn**: Điều chỉnh tốc độ trộn
5. **Hệ thống tưới**: Điều khiển máy bơm

## 🎉 **Kết quả:**
Bây giờ bạn có thể điều khiển cả LED và động cơ DC từ một giao diện duy nhất, hoạt động offline hoàn toàn!
