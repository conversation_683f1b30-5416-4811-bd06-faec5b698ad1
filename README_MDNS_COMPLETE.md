# 🚀 ESP8266 Smart Control với mDNS - <PERSON><PERSON><PERSON> bản hoàn chỉnh

## 📋 Tóm tắt
Chương trình ESP8266 hoàn chỉnh với mDNS, điều khiển LED và Motor DC. Truy cập qua `http://esp.local` thay vì phải nhớ địa chỉ IP.

## ✨ Tính năng
- ✅ **mDNS** - Truy cập qua `http://esp.local`
- ✅ **Auto AP Mode** - Tự động phát WiFi khi không kết nối được
- ✅ **Điều khiển LED** - Bật/tắt LED
- ✅ **Điều khiển Motor DC** - Thuận/nghịch/dừng + điều chỉnh tốc độ
- ✅ **Giao diện đẹp** - Dark theme, responsive
- ✅ **Thông tin hệ thống** - IP, WiFi, memory, uptime
- ✅ **Auto Reconnect** - Tự động kết nối lại WiFi
- ✅ **Serial Monitor** - Thông tin chi tiết

## ⚡ Bắt đầu nhanh

### 1. C<PERSON>u hình WiFi và AP
Mở `src/main.cpp` và thay đổi:
```cpp
// WiFi để kết nối
const char* ssid = "TEN_WIFI_CUA_BAN";        // ← Thay đổi này
const char* password = "MAT_KHAU_WIFI";       // ← Thay đổi này

// Access Point (khi không có WiFi)
const char* ap_ssid = "ESP8266-SmartHome";    // ← Có thể thay đổi
const char* ap_password = "12345678";         // ← Có thể thay đổi

// mDNS
const char* mdns_name = "esp";                // ← Có thể thay đổi
```

### 2. Cấu hình Hardware (tùy chọn)
```cpp
// Chân LED
const int LED_PIN = 2;  // GPIO2 (D4 trên NodeMCU)

// Chân Motor DC
const int MOTOR_PIN1 = 4;   // GPIO4 (D2) - Hướng 1
const int MOTOR_PIN2 = 5;   // GPIO5 (D1) - Hướng 2  
const int MOTOR_ENABLE = 14; // GPIO14 (D5) - PWM tốc độ
```

### 3. Upload và chạy
```bash
pio run --target upload
pio device monitor
```

### 4. Truy cập

#### Khi kết nối WiFi thành công:
- **mDNS**: `http://esp.local` 🎯
- **IP**: `http://192.168.1.xxx` (xem Serial Monitor)

#### Khi không có WiFi (chế độ AP):
- **Kết nối WiFi**: `ESP8266-SmartHome` (password: `12345678`)
- **Truy cập**: `http://***********` hoặc `http://esp.local`

## 🔧 Cách hoạt động Auto AP Mode

### 🔄 Quy trình tự động:
1. **Khởi động**: ESP8266 thử kết nối WiFi trong 20 giây
2. **Thành công**: Sử dụng WiFi + mDNS (`http://esp.local`)
3. **Thất bại**: Tự động chuyển sang chế độ Access Point
4. **AP Mode**: Phát WiFi `ESP8266-SmartHome`
5. **Tự động thử lại**: Mỗi 30 giây thử kết nối WiFi lại
6. **Kết nối lại**: Tự động chuyển về WiFi mode khi có mạng

### 📱 Sử dụng AP Mode:
```
1. Kết nối điện thoại/laptop vào WiFi: ESP8266-SmartHome
2. Nhập password: 12345678
3. Truy cập: http://***********
4. Điều khiển thiết bị như bình thường
5. ESP8266 sẽ tự động thử kết nối WiFi lại
```

## 🔧 Cấu hình nâng cao

### Thay đổi tên miền
```cpp
const char* mdns_name = "smarthome";  // → http://smarthome.local
const char* mdns_name = "iot";        // → http://iot.local
const char* mdns_name = "esp32";      // → http://esp32.local
```

### Thay đổi thông tin AP
```cpp
const char* ap_ssid = "MyESP8266";       // Tên WiFi tùy chỉnh
const char* ap_password = "mypassword";  // Password tùy chỉnh (≥8 ký tự)
```

### Kiểm tra mDNS hoạt động
```bash
# Ping để test
ping esp.local

# Nếu không hoạt động, sử dụng IP trực tiếp
ping ***********00  # IP từ Serial Monitor
```

## 🔌 Kết nối Hardware

### LED
```
ESP8266    LED
GPIO2  →   Anode (+)
GND    →   Cathode (-) qua điện trở 220Ω
```

### Motor DC với L298N
```
ESP8266    L298N
GPIO4  →   IN1
GPIO5  →   IN2
GPIO14 →   ENA (PWM)
5V     →   VCC
GND    →   GND
```

## 📱 Hỗ trợ mDNS trên các thiết bị

| Thiết bị | Hỗ trợ | Cách bật |
|----------|--------|----------|
| **Windows** | ⚠️ Cần cài đặt | Cài iTunes hoặc Bonjour Print Services |
| **macOS** | ✅ Sẵn có | Không cần làm gì |
| **Linux** | ⚠️ Cần cài đặt | `sudo apt install avahi-daemon` |
| **Android** | ⚠️ App | Cài "Bonjour Browser" |
| **iOS** | ✅ Sẵn có | Không cần làm gì |

## 🎮 Cách sử dụng giao diện

### Điều khiển LED
- **BẬT**: Bật LED
- **TẮT**: Tắt LED
- Trạng thái hiển thị real-time

### Điều khiển Motor
- **THUẬN**: Quay thuận chiều kim đồng hồ
- **NGHỊCH**: Quay ngược chiều kim đồng hồ  
- **DỪNG**: Dừng motor
- **Slider tốc độ**: Điều chỉnh tốc độ 0-255

## 🔍 Troubleshooting

### ❌ Không truy cập được esp.local

**Nguyên nhân**: Thiết bị không hỗ trợ mDNS

**Giải pháp**:
1. Sử dụng IP trực tiếp (xem Serial Monitor)
2. Cài đặt Bonjour cho Windows
3. Kiểm tra cùng mạng WiFi

### ❌ ESP8266 không kết nối WiFi

**Nguyên nhân**: Sai thông tin WiFi hoặc tín hiệu yếu

**Giải pháp**:
1. Kiểm tra SSID và password
2. Đảm bảo WiFi 2.4GHz (không phải 5GHz)
3. Đưa ESP8266 gần router hơn

### ❌ LED/Motor không hoạt động

**Nguyên nhân**: Sai kết nối hardware hoặc chân GPIO

**Giải pháp**:
1. Kiểm tra kết nối dây
2. Kiểm tra định nghĩa chân trong code
3. Kiểm tra nguồn cung cấp

### ❌ Serial Monitor không hiển thị gì

**Nguyên nhân**: Sai baud rate hoặc port

**Giải pháp**:
```bash
# Kiểm tra port
pio device list

# Sử dụng baud rate đúng (115200)
pio device monitor --baud 115200
```

## 📊 Thông tin hiển thị trên web

### Điều khiển thiết bị
- 💡 **LED Control**: Bật/tắt với trạng thái real-time
- ⚙️ **Motor Control**: Điều khiển hướng và tốc độ

### Thông tin hệ thống
- 📡 **WiFi**: SSID, IP, Gateway, RSSI
- ⚡ **System**: Uptime, Memory, CPU frequency
- 🔗 **Access**: IP và mDNS links

## 📝 Thông tin Serial Monitor

Khi chạy thành công:

```
========================================
🚀 ESP8266 Smart Control với mDNS
========================================
✅ Đã khởi tạo các chân GPIO
📡 Đang kết nối WiFi: TEN_WIFI
.....
✅ WiFi đã kết nối thành công!
========================================
📊 THÔNG TIN MẠNG:
🌐 Địa chỉ IP: ***********00
🚪 Gateway: ***********
📶 Cường độ tín hiệu: -45 dBm
========================================
🔧 Đang cấu hình mDNS: esp.local
✅ mDNS đã khởi tạo thành công!
🌐 Truy cập qua: http://esp.local
📡 HTTP service đã được đăng ký
🌐 Web server đã khởi động!
========================================
🎉 SẴN SÀNG SỬ DỤNG!
Truy cập bằng một trong các cách sau:
   • http://***********00
   • http://esp.local
========================================
```

## 🎯 Ứng dụng thực tế

### Phù hợp cho:
- ✅ Smart Home control
- ✅ IoT projects với giao diện web
- ✅ Học tập về ESP8266 + mDNS
- ✅ Prototype nhanh cho dự án

### Mở rộng thêm:
- 🔄 Thêm cảm biến (nhiệt độ, độ ẩm)
- 📱 Tích hợp với mobile app
- 🔐 Thêm authentication
- 📊 Data logging và charts

## 📚 Tài liệu tham khảo

- [ESP8266 Arduino Core](https://arduino-esp8266.readthedocs.io/)
- [ESP8266 mDNS Library](https://arduino-esp8266.readthedocs.io/en/latest/esp8266wifi/readme.html#mdns-and-dns-sd)
- [PlatformIO ESP8266](https://docs.platformio.org/en/latest/platforms/espressif8266.html)

---

💡 **Tip**: Chương trình này kết hợp tất cả tính năng cần thiết cho một smart home controller hoàn chỉnh với mDNS!
